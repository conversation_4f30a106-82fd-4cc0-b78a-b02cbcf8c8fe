2025-05-04 21:07:45,972 - INFO - PyPDF2 configured. PDF parsing support enabled.
2025-05-04 21:07:45,997 - INFO - win32com configured. DOC parsing support enabled.
2025-05-04 21:07:46,007 - DEBUG - antiword not available as fallback for DOC files.
2025-05-04 21:07:46,277 - INFO - Application started
2025-05-04 21:07:46,279 - INFO - Custom styling applied
2025-05-04 21:07:46,284 - INFO - rarfile configured. RAR parsing support enabled.
2025-05-04 21:07:46,455 - INFO - py7zr configured. 7z archive parsing support enabled.
2025-05-04 21:07:46,457 - DEBUG - Registered parser: Archive Parser
2025-05-04 21:07:46,457 - DEBUG - Registered parser: CSV Parser
2025-05-04 21:07:46,457 - DEBUG - Registered parser: DOC Parser
2025-05-04 21:07:46,457 - DEBUG - Registered parser: DOCX Parser
2025-05-04 21:07:46,457 - DEBUG - Registered parser: Excel Parser
2025-05-04 21:07:46,457 - DEBUG - Registered parser: ODS Parser
2025-05-04 21:07:46,457 - DEBUG - Registered parser: PDF Parser
2025-05-04 21:07:46,457 - DEBUG - Registered parser: Text Parser
2025-05-04 21:07:46,457 - INFO - Discovered 8 parser plugins
2025-05-04 21:07:46,508 - INFO - Looking for logo at (method 1): C:\Users\<USER>\Desktop\L457\SearchTools-v1\icon.ico
2025-05-04 21:07:46,508 - INFO - Logo file found at: C:\Users\<USER>\Desktop\L457\SearchTools-v1\icon.ico
2025-05-04 21:07:46,645 - INFO - Image opened, mode: RGBA, size: (159, 31)
2025-05-04 21:07:46,646 - INFO - Resized image to 100x19 to maintain aspect ratio
2025-05-04 21:07:46,656 - INFO - Converted to PhotoImage
2025-05-04 21:07:46,659 - INFO - Bottom logo loaded successfully
2025-05-04 21:07:46,907 - INFO - UI initialized
2025-05-04 21:08:22,927 - INFO - Window closed by user
2025-05-04 21:08:23,057 - INFO - Application exited
