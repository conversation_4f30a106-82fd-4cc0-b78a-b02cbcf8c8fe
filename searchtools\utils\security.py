"""
Security utilities for safe file and archive operations
"""

import os
import re
import tempfile
from typing import List, Optional, Union
from pathlib import Path

from searchtools.utils.logging import logger
from searchtools.utils.error_handling import SearchToolsError
import config


class SecurityError(SearchToolsError):
    """Exception raised for security violations."""
    pass


class PathTraversalError(SecurityError):
    """Exception raised for path traversal attempts."""
    pass


class ResourceExhaustionError(SecurityError):
    """Exception raised when resource limits are exceeded."""
    pass


class SecurePathValidator:
    """
    Validates file paths for security issues including path traversal attacks.
    """
    
    def __init__(self, strict_mode: bool = True):
        """
        Initialize the path validator.
        
        Args:
            strict_mode (bool): Enable strict validation rules
        """
        self.strict_mode = strict_mode
        self.max_path_length = config.ARCHIVE_SECURITY["max_path_length"]
        self.max_directory_depth = config.ARCHIVE_SECURITY["max_directory_depth"]
        self.blocked_extensions = [ext.lower() for ext in config.ARCHIVE_SECURITY["blocked_extensions"]]
    
    def validate_path(self, file_path: str, base_dir: str) -> str:
        """
        Validate a file path for security issues.
        
        Args:
            file_path (str): The file path to validate
            base_dir (str): The base directory that files should be extracted to
            
        Returns:
            str: The validated and normalized path
            
        Raises:
            PathTraversalError: If path traversal is detected
            SecurityError: If other security issues are found
        """
        # Normalize the path
        normalized_path = os.path.normpath(file_path)
        
        # Check for path traversal attempts
        if self._has_path_traversal(normalized_path):
            raise PathTraversalError(f"Path traversal detected in: {file_path}")
        
        # Check path length
        if len(normalized_path) > self.max_path_length:
            raise SecurityError(f"Path too long ({len(normalized_path)} > {self.max_path_length}): {file_path}")
        
        # Check directory depth
        depth = len(Path(normalized_path).parts)
        if depth > self.max_directory_depth:
            raise SecurityError(f"Directory nesting too deep ({depth} > {self.max_directory_depth}): {file_path}")
        
        # Check for blocked file extensions
        if self.strict_mode:
            self._check_blocked_extensions(normalized_path)
        
        # Ensure the final path is within the base directory
        full_path = os.path.join(base_dir, normalized_path)
        full_path = os.path.abspath(full_path)
        base_dir = os.path.abspath(base_dir)
        
        if not full_path.startswith(base_dir + os.sep) and full_path != base_dir:
            raise PathTraversalError(f"Path escapes base directory: {file_path}")
        
        return normalized_path
    
    def _has_path_traversal(self, path: str) -> bool:
        """
        Check if a path contains path traversal sequences.
        
        Args:
            path (str): The path to check
            
        Returns:
            bool: True if path traversal is detected
        """
        # Check for obvious traversal patterns
        dangerous_patterns = [
            '..',
            '/..',
            '..\\',
            '\\..\\',
            '../',
            '..\\',
        ]
        
        path_lower = path.lower()
        for pattern in dangerous_patterns:
            if pattern in path_lower:
                return True
        
        # Check for absolute paths
        if os.path.isabs(path):
            return True
        
        # Check for drive letters on Windows
        if re.match(r'^[a-zA-Z]:', path):
            return True
        
        # Check for UNC paths
        if path.startswith('\\\\') or path.startswith('//'):
            return True
        
        return False
    
    def _check_blocked_extensions(self, path: str) -> None:
        """
        Check if the file has a blocked extension.
        
        Args:
            path (str): The file path to check
            
        Raises:
            SecurityError: If the file has a blocked extension
        """
        _, ext = os.path.splitext(path)
        if ext.lower() in self.blocked_extensions:
            raise SecurityError(f"Blocked file extension: {ext}")


class ArchiveSecurityManager:
    """
    Manages security constraints for archive extraction operations.
    """
    
    def __init__(self):
        """Initialize the security manager with configuration values."""
        self.max_extracted_size = config.ARCHIVE_SECURITY["max_extracted_size"]
        self.max_files_per_archive = config.ARCHIVE_SECURITY["max_files_per_archive"]
        self.max_individual_file_size = config.ARCHIVE_SECURITY["max_individual_file_size"]
        self.path_validator = SecurePathValidator(
            strict_mode=config.ARCHIVE_SECURITY["strict_path_validation"]
        )
        
        # Tracking variables
        self.total_extracted_size = 0
        self.files_extracted = 0
    
    def reset_counters(self):
        """Reset the extraction counters for a new archive."""
        self.total_extracted_size = 0
        self.files_extracted = 0
    
    def validate_extraction(self, member_name: str, member_size: int, base_dir: str) -> str:
        """
        Validate that a file can be safely extracted.
        
        Args:
            member_name (str): Name/path of the archive member
            member_size (int): Size of the member in bytes
            base_dir (str): Base directory for extraction
            
        Returns:
            str: Validated path for extraction
            
        Raises:
            ResourceExhaustionError: If resource limits would be exceeded
            PathTraversalError: If path traversal is detected
            SecurityError: If other security issues are found
        """
        # Validate the path
        safe_path = self.path_validator.validate_path(member_name, base_dir)
        
        # Check file count limit
        if self.files_extracted >= self.max_files_per_archive:
            raise ResourceExhaustionError(
                f"Too many files in archive (limit: {self.max_files_per_archive})"
            )
        
        # Check individual file size
        if member_size > self.max_individual_file_size:
            raise ResourceExhaustionError(
                f"File too large: {member_name} ({member_size} bytes > {self.max_individual_file_size})"
            )
        
        # Check total extracted size
        if self.total_extracted_size + member_size > self.max_extracted_size:
            raise ResourceExhaustionError(
                f"Archive too large when extracted (limit: {self.max_extracted_size} bytes)"
            )
        
        # Update counters
        self.total_extracted_size += member_size
        self.files_extracted += 1
        
        logger.debug(f"Validated extraction: {safe_path} ({member_size} bytes)")
        return safe_path
    
    def get_extraction_stats(self) -> dict:
        """
        Get current extraction statistics.
        
        Returns:
            dict: Statistics about the current extraction
        """
        return {
            "files_extracted": self.files_extracted,
            "total_size": self.total_extracted_size,
            "max_files": self.max_files_per_archive,
            "max_size": self.max_extracted_size,
            "files_remaining": max(0, self.max_files_per_archive - self.files_extracted),
            "size_remaining": max(0, self.max_extracted_size - self.total_extracted_size)
        }


def create_secure_temp_dir(prefix: str = None) -> str:
    """
    Create a temporary directory with secure permissions.
    
    Args:
        prefix (str): Prefix for the directory name
        
    Returns:
        str: Path to the created temporary directory
    """
    if prefix is None:
        prefix = config.TEMP_DIR_SECURITY["temp_dir_prefix"]
    
    temp_dir = tempfile.mkdtemp(prefix=prefix)
    
    # Set secure permissions (owner read/write/execute only)
    try:
        os.chmod(temp_dir, 0o700)
    except OSError as e:
        logger.warning(f"Could not set secure permissions on temp dir {temp_dir}: {e}")
    
    logger.debug(f"Created secure temporary directory: {temp_dir}")
    return temp_dir
