"""
Base parser interface for file parsers
"""

from abc import ABC, abstractmethod
from typing import List

from searchtools.core.result import SearchResult

class BaseParser(ABC):
    """
    Abstract base class for all file parsers.

    All parser plugins must inherit from this class and implement the required methods.
    """

    @classmethod
    @abstractmethod
    def get_name(cls) -> str:
        """
        Get the name of the parser.

        Returns:
            str: Parser name
        """
        pass

    @classmethod
    @abstractmethod
    def get_supported_extensions(cls) -> List[str]:
        """
        Get the list of file extensions supported by this parser.

        Returns:
            List[str]: List of supported file extensions (e.g., ['.txt', '.log'])
        """
        pass

    @classmethod
    @abstractmethod
    def is_available(cls) -> bool:
        """
        Check if the parser is available (all required dependencies are installed).

        Returns:
            bool: True if the parser is available, False otherwise
        """
        return True

    @classmethod
    @abstractmethod
    def parse(cls, file_path: str, file_name: str, keywords: List[str], case_sensitive: bool = False,
             use_regex: bool = False, whole_word: bool = False) -> List[SearchResult]:
        """
        Parse a file and search for keywords.

        Args:
            file_path (str): Path to the file
            file_name (str): Name of the file
            keywords (list): List of keywords to search for
            case_sensitive (bool, optional): Whether to use case-sensitive search. Defaults to False.
            use_regex (bool, optional): Whether to treat keywords as regular expressions. Defaults to False.
            whole_word (bool, optional): Whether to match whole words only. Defaults to False.

        Returns:
            list: List of SearchResult objects
        """
        pass

    @classmethod
    def get_priority(cls) -> int:
        """
        Get the priority of the parser. Higher priority parsers are tried first.

        Returns:
            int: Priority value (default: 0)
        """
        return 0

    @classmethod
    def can_parse(cls, file_path: str) -> bool:
        """
        Check if this parser can parse the given file.

        Args:
            file_path (str): Path to the file

        Returns:
            bool: True if the parser can parse the file, False otherwise
        """
        if not cls.is_available():
            return False

        # Check file extension
        from searchtools.utils.file_utils import get_file_extension
        ext = get_file_extension(file_path)
        return ext.lower() in [e.lower() for e in cls.get_supported_extensions()]
