"""
Example of using the ErrorHandlingPolicy in real code
"""

import os
import sys

# Add the parent directory to the path so we can import searchtools
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from searchtools.utils.error_handling import (
    ErrorHandlingPolicy, ErrorAction, LogLevel,
    handle_parser_error, handle_file_operation, safe_operation,
    FileReadError, EncodingError
)

# Example 1: Using handle_parser_error decorator
@handle_parser_error
def parse_csv_file(file_path, file_name):
    """
    Parse a CSV file and return the data.

    This function uses the default PARSER_POLICY, which returns an empty list on error.
    """
    print(f"Parsing CSV file: {file_path}")

    # Simulate parsing
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"File not found: {file_path}")

    # Return some dummy data
    return [
        {"name": "<PERSON>", "age": 30},
        {"name": "<PERSON>", "age": 25}
    ]

# Example 2: Using handle_file_operation decorator with custom policy
file_policy = ErrorHandlingPolicy(
    action=ErrorAction.RETURN_DEFAULT,
    log_level=LogLevel.WARNING,
    default_value="File not found"
)

@handle_file_operation(policy=file_policy)
def read_file_content(file_path):
    """
    Read the content of a file.

    This function uses a custom policy that returns "File not found" on error.
    """
    print(f"Reading file: {file_path}")

    with open(file_path, 'r') as f:
        return f.read()

# Example 3: Using safe_operation function
def process_data(data):
    """
    Process data safely using safe_operation.
    """
    print("Processing data...")

    # Define a custom policy for data processing
    process_policy = ErrorHandlingPolicy(
        action=ErrorAction.LOG_AND_CONTINUE,
        log_level=LogLevel.ERROR,
        include_traceback=True
    )

    # Process the data safely
    result = safe_operation(
        lambda: data["key"] * 2,  # This will raise KeyError if "key" doesn't exist
        "Failed to process data",
        policy=process_policy,
        context={"data_type": type(data).__name__}
    )

    return result or "Default result"

def main():
    """
    Main function to demonstrate error handling.
    """
    print("Error Handling Example")
    print("=====================")

    # Example 1: Parse a CSV file
    print("\nExample 1: Parse a CSV file")
    print("--------------------------")

    # Try with an existing file
    result1 = parse_csv_file(__file__, os.path.basename(__file__))
    print(f"Result with existing file: {result1}")

    # Try with a non-existing file
    result2 = parse_csv_file("non_existing_file.csv", "non_existing_file.csv")
    print(f"Result with non-existing file: {result2}")

    # Example 2: Read a file
    print("\nExample 2: Read a file")
    print("--------------------")

    # Try with an existing file
    result3 = read_file_content(__file__)
    print(f"Result with existing file: {result3[:50]}...")

    # Try with a non-existing file
    result4 = read_file_content("non_existing_file.txt")
    print(f"Result with non-existing file: {result4}")

    # Example 3: Process data
    print("\nExample 3: Process data")
    print("--------------------")

    # Try with valid data
    result5 = process_data({"key": 5})
    print(f"Result with valid data: {result5}")

    # Try with invalid data
    result6 = process_data({"wrong_key": 5})
    print(f"Result with invalid data: {result6}")

    print("\nAll examples completed.")

if __name__ == "__main__":
    main()
