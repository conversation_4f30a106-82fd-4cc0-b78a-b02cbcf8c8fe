2025-05-04 21:01:28,407 - INFO - PyPDF2 configured. PDF parsing support enabled.
2025-05-04 21:01:28,442 - INFO - win32com configured. DOC parsing support enabled.
2025-05-04 21:01:28,446 - DEBUG - antiword not available as fallback for DOC files.
2025-05-04 21:01:28,695 - INFO - Application started
2025-05-04 21:01:28,696 - INFO - Custom styling applied
2025-05-04 21:01:28,699 - INFO - rarfile configured. RAR parsing support enabled.
2025-05-04 21:01:28,836 - INFO - py7zr configured. 7z archive parsing support enabled.
2025-05-04 21:01:28,836 - DEBUG - Registered parser: Archive Parser
2025-05-04 21:01:28,840 - DEBUG - Registered parser: CSV Parser
2025-05-04 21:01:28,842 - DEBUG - Registered parser: DOC Parser
2025-05-04 21:01:28,842 - DEBUG - Registered parser: DOCX Parser
2025-05-04 21:01:28,842 - DEBUG - Registered parser: Excel Parser
2025-05-04 21:01:28,842 - DEBUG - Registered parser: ODS Parser
2025-05-04 21:01:28,844 - DEBUG - Registered parser: PDF Parser
2025-05-04 21:01:28,844 - DEBUG - Registered parser: Text Parser
2025-05-04 21:01:28,844 - INFO - Discovered 8 parser plugins
2025-05-04 21:01:28,855 - INFO - Looking for logo at (method 1): C:\Users\<USER>\Desktop\L457\SearchTools-v1\icon.ico
2025-05-04 21:01:28,856 - INFO - Logo file found at: C:\Users\<USER>\Desktop\L457\SearchTools-v1\icon.ico
2025-05-04 21:01:28,966 - INFO - Image opened, mode: RGBA, size: (159, 31)
2025-05-04 21:01:28,966 - INFO - Resized image to 100x19 to maintain aspect ratio
2025-05-04 21:01:28,982 - INFO - Converted to PhotoImage
2025-05-04 21:01:28,987 - INFO - Bottom logo loaded successfully
2025-05-04 21:01:29,180 - INFO - UI initialized
2025-05-04 21:01:59,157 - INFO - Window closed by user
2025-05-04 21:01:59,332 - INFO - Application exited
