# Logging Improvements in SearchTools

This document summarizes the improvements made to address inconsistent logging levels in the SearchTools project.

## Problem

The codebase had inconsistent logging levels across different modules:

1. Some errors were logged as warnings and vice versa
2. Similar events were logged at different levels in different modules
3. Log messages lacked a consistent format
4. There was no clear guideline for when to use each logging level

## Solution

We implemented a comprehensive solution to standardize logging across the codebase:

1. Created a logging guidelines document (`docs/logging_guidelines.md`)
2. Implemented a logging guidelines module (`searchtools/utils/logging_guidelines.py`)
3. Updated the PDF parser as an example of using the new guidelines
4. Created tests to verify the logging guidelines implementation

## Implementation Details

### 1. Logging Guidelines Document

Created a comprehensive document that outlines:
- When to use each logging level
- Standard formats for log messages
- Best practices for logging
- Examples of good and bad logging practices

### 2. Logging Guidelines Module

Implemented a module that provides:
- Standard log messages for common scenarios
- Helper functions for consistent logging
- Documentation for each function

The module includes helper functions for:
- Parser-related logging
- File operation logging
- Archive operation logging
- Dependency status logging
- Search operation logging

### 3. PDF Parser Updates

Updated the PDF parser to use the new logging guidelines:
- Replaced direct logger calls with helper functions
- Standardized log levels for different events
- Improved log message clarity and consistency

### 4. Testing

Created a test script (`test_logging_guidelines.py`) to verify:
- All helper functions work correctly
- Log messages are formatted consistently
- Log levels are appropriate for each event

## Benefits

These improvements provide several benefits:

1. **Consistency**: All modules now use the same logging levels for similar events
2. **Clarity**: Log messages are more informative and follow a consistent format
3. **Maintainability**: New code can easily follow the established patterns
4. **Debuggability**: More useful logs make it easier to diagnose issues

## Example

Before:
```python
# Inconsistent levels and formats
logger.warning(f"PDF parsing not available for {file_name}")
logger.warning(f"PDF parsing failed for {file_name}: {e}")
```

After:
```python
# Consistent levels and formats using helper functions
log_parser_not_available("PDF", file_name)
log_parser_error("PDF", file_name, e)
```

## Implementation Progress

We have made significant progress in standardizing logging across the codebase:

1. ✅ Created a logging guidelines module with helper functions
2. ✅ Updated the PDF parser to use the new logging guidelines
3. ✅ Updated the Excel parser to use the new logging guidelines
4. ✅ Updated the DOC parser to use the new logging guidelines
5. ⬜ Update remaining parser classes to use the new logging guidelines
6. ⬜ Update the scanner class to use the new logging guidelines
7. ⬜ Update utility modules to use the new logging guidelines
8. ⬜ Add logging level configuration to the application settings

## Next Steps

To complete the standardization of logging across the codebase:

1. Update the remaining parser classes (Archive, Text, etc.)
2. Update the scanner class to use the new logging guidelines
3. Update utility modules to use the new logging guidelines
4. Add logging level configuration to the application settings
