"""
Secure temporary directory management with context managers and cleanup handlers
"""

import os
import atexit
import shutil
import tempfile
import threading
import time
from typing import List, Optional, Set
from contextlib import contextmanager

from searchtools.utils.logging import logger
from searchtools.utils.security import create_secure_temp_dir
from searchtools.utils.error_handling import safe_operation, ErrorHandlingPolicy, ErrorAction, LogLevel
import config


class SecureTempDirManager:
    """
    Manages temporary directories with automatic cleanup and security features.
    """
    
    def __init__(self):
        """Initialize the temporary directory manager."""
        self._temp_dirs: Set[str] = set()
        self._lock = threading.RLock()
        self._cleanup_registered = False
        self._max_temp_dirs = config.TEMP_DIR_SECURITY["max_temp_dirs"]
        self._max_age = config.TEMP_DIR_SECURITY["max_temp_dir_age"]
        self._cleanup_on_exit = config.TEMP_DIR_SECURITY["cleanup_on_exit"]
        self._periodic_cleanup = config.TEMP_DIR_SECURITY["periodic_cleanup"]
        self._cleanup_interval = config.TEMP_DIR_SECURITY["cleanup_interval"]
        self._secure_deletion = config.TEMP_DIR_SECURITY["secure_deletion"]
        
        # Register cleanup handlers
        if self._cleanup_on_exit and not self._cleanup_registered:
            atexit.register(self.cleanup_all)
            self._cleanup_registered = True
        
        # Start periodic cleanup if enabled
        if self._periodic_cleanup:
            self._start_periodic_cleanup()
    
    def create_temp_dir(self, prefix: str = None) -> str:
        """
        Create a new temporary directory.
        
        Args:
            prefix (str): Prefix for the directory name
            
        Returns:
            str: Path to the created temporary directory
            
        Raises:
            RuntimeError: If maximum number of temp directories is exceeded
        """
        with self._lock:
            # Check if we've exceeded the maximum number of temp directories
            if len(self._temp_dirs) >= self._max_temp_dirs:
                # Try to clean up old directories first
                self._cleanup_old_dirs()
                
                # If still at limit, raise an error
                if len(self._temp_dirs) >= self._max_temp_dirs:
                    raise RuntimeError(f"Maximum number of temporary directories exceeded ({self._max_temp_dirs})")
            
            # Create the directory
            if prefix is None:
                prefix = config.TEMP_DIR_SECURITY["temp_dir_prefix"]
            
            temp_dir = create_secure_temp_dir(prefix=prefix)
            self._temp_dirs.add(temp_dir)
            
            logger.debug(f"Created temporary directory: {temp_dir}")
            return temp_dir
    
    @contextmanager
    def temp_directory(self, prefix: str = None):
        """
        Context manager for temporary directories with automatic cleanup.
        
        Args:
            prefix (str): Prefix for the directory name
            
        Yields:
            str: Path to the temporary directory
        """
        temp_dir = self.create_temp_dir(prefix=prefix)
        try:
            yield temp_dir
        finally:
            self.cleanup_temp_dir(temp_dir)
    
    def cleanup_temp_dir(self, temp_dir: str) -> bool:
        """
        Clean up a specific temporary directory.
        
        Args:
            temp_dir (str): Path to the temporary directory to clean up
            
        Returns:
            bool: True if cleanup was successful, False otherwise
        """
        with self._lock:
            if temp_dir not in self._temp_dirs:
                logger.debug(f"Temporary directory not managed by this instance: {temp_dir}")
                return True
            
            success = self._remove_directory(temp_dir)
            if success:
                self._temp_dirs.discard(temp_dir)
                logger.debug(f"Cleaned up temporary directory: {temp_dir}")
            else:
                logger.warning(f"Failed to clean up temporary directory: {temp_dir}")
            
            return success
    
    def cleanup_all(self) -> int:
        """
        Clean up all managed temporary directories.
        
        Returns:
            int: Number of directories successfully cleaned up
        """
        with self._lock:
            temp_dirs_copy = self._temp_dirs.copy()
            cleaned_count = 0
            
            for temp_dir in temp_dirs_copy:
                if self.cleanup_temp_dir(temp_dir):
                    cleaned_count += 1
            
            logger.info(f"Cleaned up {cleaned_count} temporary directories")
            return cleaned_count
    
    def get_managed_dirs(self) -> List[str]:
        """
        Get a list of currently managed temporary directories.
        
        Returns:
            List[str]: List of temporary directory paths
        """
        with self._lock:
            return list(self._temp_dirs)
    
    def get_stats(self) -> dict:
        """
        Get statistics about managed temporary directories.
        
        Returns:
            dict: Statistics including count, total size, etc.
        """
        with self._lock:
            stats = {
                "count": len(self._temp_dirs),
                "max_count": self._max_temp_dirs,
                "total_size": 0,
                "directories": []
            }
            
            for temp_dir in self._temp_dirs:
                if os.path.exists(temp_dir):
                    dir_size = self._get_directory_size(temp_dir)
                    dir_age = time.time() - os.path.getctime(temp_dir)
                    
                    stats["total_size"] += dir_size
                    stats["directories"].append({
                        "path": temp_dir,
                        "size": dir_size,
                        "age": dir_age
                    })
            
            return stats
    
    def _cleanup_old_dirs(self) -> int:
        """
        Clean up directories that exceed the maximum age.
        
        Returns:
            int: Number of directories cleaned up
        """
        current_time = time.time()
        old_dirs = []
        
        for temp_dir in self._temp_dirs:
            if os.path.exists(temp_dir):
                dir_age = current_time - os.path.getctime(temp_dir)
                if dir_age > self._max_age:
                    old_dirs.append(temp_dir)
        
        cleaned_count = 0
        for temp_dir in old_dirs:
            if self.cleanup_temp_dir(temp_dir):
                cleaned_count += 1
        
        if cleaned_count > 0:
            logger.info(f"Cleaned up {cleaned_count} old temporary directories")
        
        return cleaned_count
    
    def _remove_directory(self, temp_dir: str) -> bool:
        """
        Safely remove a directory and its contents.
        
        Args:
            temp_dir (str): Path to the directory to remove
            
        Returns:
            bool: True if removal was successful, False otherwise
        """
        if not os.path.exists(temp_dir):
            return True
        
        def _remove_dir():
            if self._secure_deletion:
                # Secure deletion: overwrite files before deletion
                self._secure_delete_directory(temp_dir)
            else:
                # Standard deletion
                shutil.rmtree(temp_dir)
        
        # Create a policy for directory removal
        policy = ErrorHandlingPolicy(
            action=ErrorAction.LOG_AND_CONTINUE,
            log_level=LogLevel.ERROR,
            include_traceback=True
        )
        
        result = safe_operation(
            _remove_dir,
            f"Error removing temporary directory {temp_dir}",
            policy=policy
        )
        
        return result is not None and not os.path.exists(temp_dir)
    
    def _secure_delete_directory(self, temp_dir: str) -> None:
        """
        Securely delete a directory by overwriting files before deletion.
        
        Args:
            temp_dir (str): Path to the directory to securely delete
        """
        # Walk through all files and overwrite them
        for root, dirs, files in os.walk(temp_dir):
            for file in files:
                file_path = os.path.join(root, file)
                try:
                    # Overwrite file with random data
                    file_size = os.path.getsize(file_path)
                    with open(file_path, 'wb') as f:
                        f.write(os.urandom(file_size))
                    
                    # Remove the file
                    os.remove(file_path)
                except Exception as e:
                    logger.debug(f"Error securely deleting file {file_path}: {e}")
        
        # Remove the directory structure
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    def _get_directory_size(self, temp_dir: str) -> int:
        """
        Calculate the total size of a directory.
        
        Args:
            temp_dir (str): Path to the directory
            
        Returns:
            int: Total size in bytes
        """
        total_size = 0
        try:
            for dirpath, dirnames, filenames in os.walk(temp_dir):
                for filename in filenames:
                    file_path = os.path.join(dirpath, filename)
                    if os.path.exists(file_path):
                        total_size += os.path.getsize(file_path)
        except Exception as e:
            logger.debug(f"Error calculating directory size for {temp_dir}: {e}")
        
        return total_size
    
    def _start_periodic_cleanup(self) -> None:
        """Start periodic cleanup in a background thread."""
        def cleanup_worker():
            while True:
                time.sleep(self._cleanup_interval)
                try:
                    self._cleanup_old_dirs()
                except Exception as e:
                    logger.error(f"Error in periodic cleanup: {e}")
        
        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()
        logger.debug("Started periodic cleanup thread")


# Global instance for application-wide use
temp_manager = SecureTempDirManager()
