"""
Command-line interface for SearchTools
"""

import os
import sys
import argparse
import json
import csv
from typing import List, Optional
from datetime import datetime

from searchtools.utils.logging import logger
from searchtools.core.scanner import Scanner
from searchtools.core.result import SearchResult
from searchtools.parsers.plugin_manager import plugin_manager
from searchtools.utils.error_handling import handle_cli_operation
from searchtools.utils.events import EventType, event_dispatcher

class CLI:
    """
    Command-line interface for SearchTools.
    """

    def __init__(self):
        """
        Initialize the CLI.
        """
        self.parser = self._create_parser()
        self.scanner = Scanner()
        self.results: List[SearchResult] = []

        # Set up scanner callbacks
        self.scanner.set_callbacks(
            progress_callback=self._update_progress,
            status_callback=self._update_status,
            result_callback=self._add_result
        )

        # Subscribe to scanner events
        self._subscribe_to_events()

    def _create_parser(self) -> argparse.ArgumentParser:
        """
        Create the argument parser.

        Returns:
            argparse.ArgumentParser: Argument parser
        """
        parser = argparse.ArgumentParser(
            description="SearchTools - Search for keywords in files",
            formatter_class=argparse.RawTextHelpFormatter
        )

        # Required arguments
        parser.add_argument(
            "folder",
            help="Root folder to scan"
        )

        parser.add_argument(
            "keywords",
            help="Comma-separated list of keywords to search for"
        )

        # Optional arguments
        parser.add_argument(
            "-c", "--case-sensitive",
            action="store_true",
            help="Enable case-sensitive search"
        )

        parser.add_argument(
            "-o", "--output",
            help="Output file path (default: results_YYYY-MM-DD_HH-MM-SS.csv)"
        )

        parser.add_argument(
            "-f", "--format",
            choices=["csv", "json", "text"],
            default="csv",
            help="Output format (default: csv)"
        )

        parser.add_argument(
            "-v", "--verbose",
            action="store_true",
            help="Enable verbose output"
        )

        parser.add_argument(
            "--list-parsers",
            action="store_true",
            help="List available file parsers and exit"
        )

        return parser

    def _subscribe_to_events(self):
        """
        Subscribe to scanner events.
        """
        # Subscribe to progress update events
        event_dispatcher.subscribe(
            EventType.PROGRESS_UPDATE,
            self._handle_progress_event
        )

        # Subscribe to status update events
        event_dispatcher.subscribe(
            EventType.STATUS_UPDATE,
            self._handle_status_event
        )

        # Subscribe to result found events
        event_dispatcher.subscribe(
            EventType.RESULT_FOUND,
            self._handle_result_event
        )

    @handle_cli_operation(error_message="Error handling progress event")
    def _handle_progress_event(self, event):
        """
        Handle progress update events.

        Args:
            event (Event): Progress update event
        """
        progress = event.data.get("progress", 0)
        self._update_progress(progress)

    @handle_cli_operation(error_message="Error handling status event")
    def _handle_status_event(self, event):
        """
        Handle status update events.

        Args:
            event (Event): Status update event
        """
        status = event.data.get("status", "")
        self._update_status(status)

    @handle_cli_operation(error_message="Error handling result event")
    def _handle_result_event(self, event):
        """
        Handle result found events.

        Args:
            event (Event): Result found event
        """
        result = event.data.get("result")
        if result:
            self._add_result(result)

    @handle_cli_operation(error_message="Error running CLI command", exit_on_error=True)
    def run(self, args: Optional[List[str]] = None) -> int:
        """
        Run the CLI with the given arguments.

        Args:
            args (List[str], optional): Command-line arguments. Defaults to None.

        Returns:
            int: Exit code
        """
        # Parse arguments
        args = self.parser.parse_args(args)

        # Initialize plugin manager
        plugin_manager.discover_parsers()

        # List parsers and exit if requested
        if args.list_parsers:
            self._list_parsers()
            return 0

        # Parse keywords
        keywords = [k.strip() for k in args.keywords.split(',') if k.strip()]

        if not keywords:
            print("Error: No valid keywords provided")
            return 1

        # Check if folder exists
        if not os.path.isdir(args.folder):
            print(f"Error: Folder not found: {args.folder}")
            return 1

        # Set up output file
        if not args.output:
            timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
            args.output = f"results_{timestamp}.{args.format}"

        # Start scanning
        print(f"Scanning folder: {args.folder}")
        print(f"Keywords: {', '.join(keywords)}")
        print(f"Case-sensitive: {args.case_sensitive}")

        # Clear previous results
        self.results = []

        try:
            # Start the scan
            self.scanner.start_scan(args.folder, keywords, args.case_sensitive)

            # Wait for the scan to complete
            import time
            while self.scanner.scan_in_progress:
                time.sleep(0.1)

            # Get results
            self.results = self.scanner.get_results()

            # Print results summary
            print(f"\nFound {len(self.results)} matches")

            # Export results
            if self.results:
                self._export_results(args.output, args.format)
                print(f"Results exported to: {args.output}")
        finally:
            # Clean up (always executed, even if an exception occurs)
            self.scanner.cleanup_temp_dirs()

            # Unsubscribe from events
            self._unsubscribe_from_events()

        return 0

    def _unsubscribe_from_events(self):
        """
        Unsubscribe from scanner events.
        """
        # Unsubscribe from events
        event_dispatcher.unsubscribe(EventType.PROGRESS_UPDATE, self._handle_progress_event)
        event_dispatcher.unsubscribe(EventType.STATUS_UPDATE, self._handle_status_event)
        event_dispatcher.unsubscribe(EventType.RESULT_FOUND, self._handle_result_event)

    @handle_cli_operation(error_message="Error updating progress")
    def _update_progress(self, progress: float) -> None:
        """
        Update the progress bar.

        Args:
            progress (float): Progress value (0-100)
        """
        # Print progress bar
        bar_length = 40
        filled_length = int(bar_length * progress / 100)
        bar = '█' * filled_length + '-' * (bar_length - filled_length)
        sys.stdout.write(f"\rProgress: [{bar}] {progress:.1f}%")
        sys.stdout.flush()

    @handle_cli_operation(error_message="Error updating status")
    def _update_status(self, status: str) -> None:
        """
        Update the status text.

        Args:
            status (str): Status text
        """
        # Print status on a new line if it's a significant status change
        if "complete" in status.lower() or "stopped" in status.lower():
            print(f"\n{status}")

    @handle_cli_operation(error_message="Error adding search result")
    def _add_result(self, result: SearchResult) -> None:
        """
        Add a search result.

        Args:
            result (SearchResult): Search result to add
        """
        # Skip archive markers
        if result.keyword == "ARCHIVE_MARKER":
            return

        # Add to results list
        self.results.append(result)

    @handle_cli_operation(error_message="Error exporting results")
    def _export_results(self, output_path: str, format_type: str) -> None:
        """
        Export results to a file.

        Args:
            output_path (str): Output file path
            format_type (str): Output format (csv, json, text)
        """
        if format_type == "csv":
            self._export_csv(output_path)
        elif format_type == "json":
            self._export_json(output_path)
        elif format_type == "text":
            self._export_text(output_path)
        else:
            logger.warning(f"Unknown export format: {format_type}, defaulting to CSV")
            self._export_csv(output_path)

    @handle_cli_operation(error_message="Error exporting to CSV")
    def _export_csv(self, output_path: str) -> None:
        """
        Export results to a CSV file.

        Args:
            output_path (str): Output file path
        """
        with open(output_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)

            # Write header
            writer.writerow(["File", "Path", "Keyword", "Context", "Location"])

            # Write data
            for result in self.results:
                writer.writerow([
                    result.file,
                    result.path,
                    result.keyword,
                    result.context,
                    result.location
                ])

    @handle_cli_operation(error_message="Error exporting to JSON")
    def _export_json(self, output_path: str) -> None:
        """
        Export results to a JSON file.

        Args:
            output_path (str): Output file path
        """
        data = []
        for result in self.results:
            data.append(result.to_dict())

        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2)

    @handle_cli_operation(error_message="Error exporting to text")
    def _export_text(self, output_path: str) -> None:
        """
        Export results to a text file.

        Args:
            output_path (str): Output file path
        """
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(f"Search Results ({len(self.results)} matches)\n")
            f.write("=" * 80 + "\n\n")

            for i, result in enumerate(self.results, 1):
                f.write(f"Match #{i}:\n")
                f.write(f"File: {result.file}\n")
                f.write(f"Path: {result.path}\n")
                f.write(f"Keyword: {result.keyword}\n")
                f.write(f"Location: {result.location}\n")
                f.write(f"Context: {result.context}\n")
                f.write("\n" + "-" * 80 + "\n\n")

    @handle_cli_operation(error_message="Error listing parsers")
    def _list_parsers(self) -> None:
        """
        List available file parsers.
        """
        print("Available File Parsers:")
        print("=" * 80)

        parsers = plugin_manager.get_all_parsers()
        for parser in parsers:
            available = "Available" if parser.is_available() else "Not Available"
            extensions = ", ".join(parser.get_supported_extensions())
            print(f"{parser.get_name()} ({available})")
            print(f"  Extensions: {extensions}")
            print(f"  Priority: {parser.get_priority()}")
            print("-" * 80)

# Create a singleton instance
cli = CLI()
