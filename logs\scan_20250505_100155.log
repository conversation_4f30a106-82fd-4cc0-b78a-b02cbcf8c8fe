2025-05-05 10:01:59,673 - INFO - PyPDF2 configured. PDF parsing support enabled.
2025-05-05 10:01:59,955 - INFO - win32com configured. DOC parsing support enabled.
2025-05-05 10:01:59,959 - DEBUG - antiword not available as fallback for DOC files.
2025-05-05 10:02:03,484 - INFO - Application started
2025-05-05 10:02:03,493 - INFO - Custom styling applied
2025-05-05 10:02:03,533 - INFO - rarfile configured. RAR parsing support enabled.
2025-05-05 10:02:04,057 - INFO - py7zr configured. 7z archive parsing support enabled.
2025-05-05 10:02:04,063 - DEBUG - Registered parser: Archive Parser
2025-05-05 10:02:04,065 - DEBUG - Registered parser: CSV Parser
2025-05-05 10:02:04,065 - DEBUG - Registered parser: DOC Parser
2025-05-05 10:02:04,065 - DEBUG - Registered parser: DOCX Parser
2025-05-05 10:02:04,068 - DEBUG - Registered parser: Excel Parser
2025-05-05 10:02:04,068 - DEBUG - Registered parser: ODS Parser
2025-05-05 10:02:04,068 - DEBUG - Registered parser: PDF Parser
2025-05-05 10:02:04,070 - DEBUG - Registered parser: Text Parser
2025-05-05 10:02:04,070 - INFO - Discovered 8 parser plugins
2025-05-05 10:02:05,242 - INFO - Looking for logo at (method 1): C:\Users\<USER>\Desktop\L457\SearchTools-v1\icon.ico
2025-05-05 10:02:05,248 - INFO - Logo file found at: C:\Users\<USER>\Desktop\L457\SearchTools-v1\icon.ico
2025-05-05 10:02:05,601 - INFO - Image opened, mode: RGBA, size: (159, 31)
2025-05-05 10:02:05,608 - INFO - Resized image to 100x19 to maintain aspect ratio
2025-05-05 10:02:05,637 - INFO - Converted to PhotoImage
2025-05-05 10:02:05,764 - INFO - Bottom logo loaded successfully
