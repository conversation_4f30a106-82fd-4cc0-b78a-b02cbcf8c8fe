"""
Parser for Excel files
"""

import os
import pandas as pd
import xlrd
import warnings
from typing import List

from searchtools.utils.logging import logger
import config
from searchtools.utils.logging_guidelines import (
    log_parser_error, log_parser_fallback,
    log_file_error, log_search_match, log_dependency_status
)
from searchtools.core.searcher import TextSearcher
from searchtools.core.result import SearchResult
from searchtools.core.result_factory import SearchResultFactory
from searchtools.parsers.base_parser import BaseParser

# Suppress warnings
warnings.filterwarnings('ignore', category=UserWarning, module='openpyxl')
warnings.filterwarnings('ignore', category=UserWarning, module='xlrd')
warnings.filterwarnings('ignore', category=pd.errors.DtypeWarning)

def safe_read_excel(file_path):
    """
    Read Excel files completely without size restrictions.

    Args:
        file_path (str): Path to the Excel file

    Returns:
        pandas.DataFrame: DataFrame containing the Excel data
    """
    # Check if file exists and is readable
    if not os.path.isfile(file_path):
        log_file_error("not_found", file_path)
        return pd.DataFrame()

    # Check file size - if too small, likely not a valid Excel file
    file_size = os.path.getsize(file_path)
    if file_size < config.MIN_EXCEL_FILE_SIZE:
        logger.warning(f"Excel file too small ({file_size} bytes), likely not a valid Excel file: {file_path}")
        return pd.DataFrame()

    # Check file header to see if it's actually an Excel file
    try:
        with open(file_path, 'rb') as f:
            header = f.read(8)
            # Check for Excel file signatures
            if not (header.startswith(b'\xD0\xCF\x11\xE0') or  # OLE2 (xls)
                    header.startswith(b'PK\x03\x04')):         # ZIP (xlsx)
                logger.warning(f"File does not have Excel signature, might not be an Excel file: {file_path}")
                # Continue anyway, as some Excel files might have different headers
    except Exception as e:
        logger.warning(f"Could not check Excel file header: {file_path}, {e}")

    try:
        # First try with openpyxl (best for xlsx)
        try:
            return pd.read_excel(
                file_path,
                dtype='object',
                na_filter=False,
                engine='openpyxl'
            )
        except Exception as e1:
            logger.debug(f"Openpyxl failed for {file_path}: {e1}. Trying xlrd...")
            log_parser_fallback("xlrd", os.path.basename(file_path))

            # Try xlrd for xls files
            try:
                return pd.read_excel(
                    file_path,
                    dtype='object',
                    na_filter=False,
                    engine='xlrd'
                )
            except Exception as e2:
                logger.debug(f"xlrd engine failed for {file_path}: {e2}. Using direct xlrd...")
                log_parser_fallback("direct xlrd", os.path.basename(file_path))

                try:
                    # Try direct xlrd as last resort
                    workbook = xlrd.open_workbook(
                        file_path,
                        on_demand=True,
                        formatting_info=False,
                        ragged_rows=True
                    )

                    # Process all sheets
                    sheet = workbook.sheet_by_index(0)
                    if sheet.nrows == 0:
                        logger.warning(f"Empty Excel sheet in {file_path}")
                        return pd.DataFrame()

                    # Create headers from first row or column indices
                    headers = (
                        [str(sheet.cell_value(0, col)) for col in range(sheet.ncols)]
                        if sheet.ncols > 0 else [f"Column_{i}" for i in range(10)]
                    )

                    # Process all data rows - no limit
                    data = []
                    for row_idx in range(1, sheet.nrows):
                        row_data = {}
                        for col_idx in range(min(sheet.ncols, len(headers))):
                            try:
                                value = sheet.cell_value(row_idx, col_idx)
                                header_key = headers[col_idx]
                                row_data[header_key] = str(value)
                            except IndexError:
                                pass  # Skip cells beyond row boundary
                        data.append(row_data)

                    return pd.DataFrame(data)
                except Exception as e3:
                    logger.debug(f"Direct xlrd failed for {file_path}: {e3}. Trying text-based fallback...")
                    log_parser_fallback("text-based", os.path.basename(file_path))

                    # Last resort: try to read as CSV or text file
                    try:
                        # Try to read as CSV
                        df = pd.read_csv(file_path, dtype='object', error_bad_lines=False, warn_bad_lines=True)
                        if not df.empty:
                            logger.info(f"Successfully read Excel file as CSV: {file_path}")
                            return df
                    except Exception as e4:
                        logger.debug(f"CSV fallback failed for {file_path}: {e4}")
                        log_parser_fallback("plain text", os.path.basename(file_path))

                        # Try to read as plain text
                        try:
                            with open(file_path, 'r', errors='ignore') as f:
                                lines = f.readlines()
                                if lines:
                                    # Create a simple DataFrame from text lines
                                    return pd.DataFrame({'Text': [line.strip() for line in lines if line.strip()]})
                        except Exception as e5:
                            logger.debug(f"Text fallback failed for {file_path}: {e5}")

                    # If all fallbacks fail, return empty DataFrame
                    logger.warning(f"All Excel reading methods failed, returning empty DataFrame for: {file_path}")
                    return pd.DataFrame()

    except Exception as e:
        log_file_error("read_error", file_path, e)
        # Return empty DataFrame instead of raising exception
        return pd.DataFrame()

class ExcelParser(BaseParser):
    """
    Parser for Excel files.
    """

    @classmethod
    def get_name(cls) -> str:
        """
        Get the name of the parser.

        Returns:
            str: Parser name
        """
        return "Excel Parser"

    @classmethod
    def get_supported_extensions(cls) -> List[str]:
        """
        Get the list of file extensions supported by this parser.

        Returns:
            List[str]: List of supported file extensions
        """
        return ['.xlsx', '.xls']

    @classmethod
    def get_priority(cls) -> int:
        """
        Get the priority of the parser.

        Returns:
            int: Priority value
        """
        return 10

    @classmethod
    def is_available(cls) -> bool:
        """
        Check if Excel parsing is available.

        Returns:
            bool: True if Excel parsing is available, False otherwise
        """
        try:
            # Check if pandas and xlrd are available - we already imported them at the module level,
            # but we need to make sure they're actually working
            if pd is not None and xlrd is not None:
                return True
            return False
        except ImportError:
            log_dependency_status("missing", "pandas/xlrd", "Excel parsing")
            return False

    @classmethod
    def parse(cls, file_path: str, file_name: str, keywords: List[str], case_sensitive: bool = False,
             use_regex: bool = False, whole_word: bool = False) -> List[SearchResult]:
        """
        Parse an Excel file and search for keywords.

        Args:
            file_path (str): Path to the file
            file_name (str): Name of the file
            keywords (list): List of keywords to search for
            case_sensitive (bool, optional): Whether to use case-sensitive search. Defaults to False.
            use_regex (bool, optional): Whether to treat keywords as regular expressions. Defaults to False.
            whole_word (bool, optional): Whether to match whole words only. Defaults to False.

        Returns:
            list: List of SearchResult objects
        """
        results = []
        try:
            df = safe_read_excel(file_path)
            if not df.empty:
                for col in df.columns:
                    for idx, cell in enumerate(df[col]):
                        if cell is not None:
                            cell_str = str(cell).strip()
                            if cell_str:
                                found_keyword = TextSearcher.search_text(cell_str, keywords, case_sensitive, use_regex, whole_word)
                                if found_keyword:
                                    location = f"Col: {col}, Row: {idx+1}"
                                    log_search_match("Excel", file_name, location, found_keyword)
                                    result = SearchResultFactory.create_generic_result(
                                        file_name=file_name,
                                        file_path=file_path,
                                        keyword=found_keyword,
                                        location_type="Excel",
                                        location_detail=location,
                                        context=cell_str
                                    )
                                    results.append(result)
        except Exception as e:
            log_parser_error("Excel", file_name, e)

        return results
