"""
Text search functionality
"""

import re
from typing import List, Optional, Dict, Tuple

from searchtools.utils.logging import logger

class TextSearcher:
    """
    Class for searching text content for keywords.
    """

    @staticmethod
    def _find_match_regex(text_to_search: str, keyword_to_search: str, original_keyword: str,
                         case_sensitive: bool = False) -> Tuple[bool, Optional[re.Match]]:
        """
        Find a match using regular expression search.

        Args:
            text_to_search (str): Text to search in
            keyword_to_search (str): Regex pattern to search for
            original_keyword (str): Original keyword for logging
            case_sensitive (bool, optional): Whether to use case-sensitive search. Defaults to False.

        Returns:
            Tuple[bool, Optional[re.Match]]: (found, match_object)
        """
        try:
            # Compile regex with appropriate flags
            flags = 0 if case_sensitive else re.IGNORECASE
            pattern = re.compile(keyword_to_search, flags)

            # Perform the search
            match = pattern.search(text_to_search)
            if match:
                logger.debug(f"  -> Regex match found: '{original_keyword}' in snippet: '{text_to_search[:100]}...'")
                return True, match

            return False, None
        except re.error as e:
            logger.warning(f"Invalid regex pattern '{keyword_to_search}': {e}")
            return False, None

    @staticmethod
    def _find_match_whole_word(text_to_search: str, keyword_to_search: str, original_keyword: str,
                              case_sensitive: bool = False) -> Tuple[bool, Optional[re.Match]]:
        """
        Find a match using whole word search.

        Args:
            text_to_search (str): Text to search in
            keyword_to_search (str): Keyword to search for
            original_keyword (str): Original keyword for logging
            case_sensitive (bool, optional): Whether to use case-sensitive search. Defaults to False.

        Returns:
            Tuple[bool, Optional[re.Match]]: (found, match_object)
        """
        # Create a pattern that matches the keyword as a whole word
        pattern = r'\b' + re.escape(keyword_to_search) + r'\b'
        flags = 0 if case_sensitive else re.IGNORECASE

        match = re.search(pattern, text_to_search, flags)
        if match:
            logger.debug(f"  -> Whole word match found: '{original_keyword}' in snippet: '{text_to_search[:100]}...'")
            return True, match

        return False, None

    @staticmethod
    def _find_match_substring(text_to_search: str, keyword_to_search: str, original_keyword: str) -> Tuple[bool, None]:
        """
        Find a match using simple substring search.

        Args:
            text_to_search (str): Text to search in
            keyword_to_search (str): Keyword to search for
            original_keyword (str): Original keyword for logging

        Returns:
            Tuple[bool, None]: (found, None)
        """
        if keyword_to_search in text_to_search:
            logger.debug(f"  -> Match found: '{original_keyword}' in snippet: '{text_to_search[:100]}...'")
            return True, None

        return False, None

    @staticmethod
    def _find_match(text_to_search: str, keyword_to_search: str, original_keyword: str,
                   use_regex: bool = False, whole_word: bool = False,
                   case_sensitive: bool = False) -> Tuple[bool, Optional[re.Match]]:
        """
        Find a match for a keyword in text based on search options.

        Args:
            text_to_search (str): Text to search in (already prepared for case sensitivity)
            keyword_to_search (str): Keyword to search for (already prepared for case sensitivity)
            original_keyword (str): Original keyword for logging
            use_regex (bool, optional): Whether to treat keyword as a regular expression. Defaults to False.
            whole_word (bool, optional): Whether to match whole words only. Defaults to False.
            case_sensitive (bool, optional): Whether to use case-sensitive search. Defaults to False.

        Returns:
            Tuple[bool, Optional[re.Match]]: (found, match_object) - found is True if match found,
                                            match_object is the regex match object if applicable
        """
        # Select the appropriate search method based on options
        if use_regex:
            return TextSearcher._find_match_regex(text_to_search, keyword_to_search, original_keyword, case_sensitive)
        elif whole_word:
            return TextSearcher._find_match_whole_word(text_to_search, keyword_to_search, original_keyword, case_sensitive)
        else:
            return TextSearcher._find_match_substring(text_to_search, keyword_to_search, original_keyword)

    @staticmethod
    def search_text(text: str, keywords: List[str], case_sensitive: bool = False,
                   use_regex: bool = False, whole_word: bool = False) -> Optional[str]:
        """
        Search for keywords in text, considering various search options.

        Args:
            text (str): Text to search in
            keywords (list): List of keywords to search for
            case_sensitive (bool, optional): Whether to use case-sensitive search. Defaults to False.
            use_regex (bool, optional): Whether to treat keywords as regular expressions. Defaults to False.
            whole_word (bool, optional): Whether to match whole words only. Defaults to False.

        Returns:
            str or None: Found keyword or None if not found
        """
        # Clean the text line itself
        cleaned_text = text.strip()
        if not cleaned_text:
            return None  # Skip empty lines

        # Prepare text for searching based on case sensitivity
        text_to_search = cleaned_text if case_sensitive else cleaned_text.lower()

        for i, keyword in enumerate(keywords):
            # Clean the keyword
            cleaned_keyword = keyword.strip()
            if not cleaned_keyword:
                continue  # Skip empty keywords

            # Prepare keyword for searching
            keyword_to_search = cleaned_keyword if case_sensitive else cleaned_keyword.lower()

            # Use the common method to find a match
            found, _ = TextSearcher._find_match(
                text_to_search,
                keyword_to_search,
                keywords[i],  # Original keyword for logging
                use_regex,
                whole_word,
                case_sensitive
            )

            if found:
                return keywords[i]  # Return the original keyword

        return None

    @staticmethod
    def _get_match_positions(text_to_search: str, keyword_to_search: str, found_keyword: str,
                            use_regex: bool, whole_word: bool, case_sensitive: bool) -> Tuple[int, int]:
        """
        Get the start and end positions of a match in text.

        Args:
            text_to_search (str): Text to search in
            keyword_to_search (str): Keyword to search for
            found_keyword (str): Original keyword that was found
            use_regex (bool): Whether to use regex search
            whole_word (bool): Whether to match whole words
            case_sensitive (bool): Whether to use case-sensitive search

        Returns:
            Tuple[int, int]: Start and end positions of the match
        """
        # Find the position of the keyword using our common method
        found, match = TextSearcher._find_match(
            text_to_search,
            keyword_to_search,
            found_keyword,
            use_regex,
            whole_word,
            case_sensitive
        )

        # Determine start and end positions based on match result
        if not found:
            # Fallback to simple search if the match method fails
            start_pos = text_to_search.find(keyword_to_search)
            end_pos = start_pos + len(keyword_to_search)
        elif match:  # We have a regex match object
            start_pos = match.start()
            end_pos = match.end()
        else:  # Simple substring match
            start_pos = text_to_search.find(keyword_to_search)
            end_pos = start_pos + len(keyword_to_search)

        return start_pos, end_pos

    @staticmethod
    def _create_context_string(text: str, start_pos: int, end_pos: int, context_chars: int) -> Tuple[str, str, str]:
        """
        Create context strings around a match.

        Args:
            text (str): Full text
            start_pos (int): Start position of match
            end_pos (int): End position of match
            context_chars (int): Number of characters to include as context

        Returns:
            Tuple[str, str, str]: prefix, matched_text, suffix
        """
        # Extract context boundaries
        context_start = max(0, start_pos - context_chars)
        context_end = min(len(text), end_pos + context_chars)

        # Get the matched text
        matched_text = text[start_pos:end_pos]

        # Create prefix with ellipsis if needed
        prefix = text[context_start:start_pos]
        if context_start > 0:
            prefix = "..." + prefix

        # Create suffix with ellipsis if needed
        suffix = text[end_pos:context_end]
        if context_end < len(text):
            suffix = suffix + "..."

        return prefix, matched_text, suffix

    @staticmethod
    def search_with_context(text: str, keywords: List[str], case_sensitive: bool = False,
                           use_regex: bool = False, whole_word: bool = False,
                           context_chars: int = 50) -> Optional[Dict[str, str]]:
        """
        Search for keywords in text and return the match with context.

        Args:
            text (str): Text to search in
            keywords (list): List of keywords to search for
            case_sensitive (bool, optional): Whether to use case-sensitive search. Defaults to False.
            use_regex (bool, optional): Whether to treat keywords as regular expressions. Defaults to False.
            whole_word (bool, optional): Whether to match whole words only. Defaults to False.
            context_chars (int, optional): Number of characters to include as context. Defaults to 50.

        Returns:
            dict or None: Dictionary with match information or None if not found
        """
        # Clean the text
        cleaned_text = text.strip()
        if not cleaned_text:
            return None

        # Find the keyword
        found_keyword = TextSearcher.search_text(
            cleaned_text, keywords, case_sensitive, use_regex, whole_word
        )

        if not found_keyword:
            return None

        # Prepare for context extraction
        text_to_search = cleaned_text if case_sensitive else cleaned_text.lower()
        keyword_to_search = found_keyword if case_sensitive else found_keyword.lower()

        # Get match positions
        start_pos, end_pos = TextSearcher._get_match_positions(
            text_to_search, keyword_to_search, found_keyword,
            use_regex, whole_word, case_sensitive
        )

        # Create context strings
        prefix, matched_text, suffix = TextSearcher._create_context_string(
            cleaned_text, start_pos, end_pos, context_chars
        )

        # Return the result
        return {
            "keyword": found_keyword,
            "context": prefix + matched_text + suffix,
            "matched_text": matched_text,
            "prefix": prefix,
            "suffix": suffix
        }
