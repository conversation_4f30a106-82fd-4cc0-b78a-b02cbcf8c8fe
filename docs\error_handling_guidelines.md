# Error Handling Guidelines

This document outlines the standardized approach to error handling in the SearchTools project.

## Introduction

Consistent error handling is crucial for maintaining a robust and maintainable codebase. The SearchTools project now uses a policy-based approach to error handling, which ensures that similar errors are handled consistently across the application.

## Error Handling Policy

The `ErrorHandlingPolicy` class defines how errors should be handled in different parts of the application. It provides a standardized way to:

1. Log errors at the appropriate level
2. Decide whether to return a default value or raise an exception
3. Include contextual information in error messages
4. Optionally include tracebacks for debugging

### Policy Actions

The `ErrorAction` enum defines the possible actions to take when an error occurs:

- `RETURN_DEFAULT`: Return a default value and log the error
- `RAISE_ORIGINAL`: Raise the original exception
- `RAISE_CUSTOM`: Raise a custom exception
- `LOG_AND_CONTINUE`: Log the error and continue (return None)
- `RETRY`: Retry the operation (not fully implemented yet)

### Log Levels

The `LogLevel` enum defines the log levels to use when logging errors:

- `ERROR`: For critical errors that prevent an operation from completing
- `WARNING`: For non-critical errors that allow partial completion
- `INFO`: For informational messages about errors
- `DEBUG`: For detailed debugging information

### Predefined Policies

The module provides several predefined policies for common scenarios:

- `PARSER_POLICY`: For parser errors (returns an empty list)
- `FILE_OPERATION_POLICY`: For file operation errors (raises a custom exception)
- `ARCHIVE_OPERATION_POLICY`: For archive operation errors (raises a custom exception)
- `SEARCH_OPERATION_POLICY`: For search operation errors (returns None)

## Using Error Handling Utilities

### Decorators

The module provides several decorators for handling errors in different contexts:

#### `handle_parser_error`

Use this decorator for parser methods:

```python
@handle_parser_error
def parse_file(file_path, file_name):
    # Parser implementation
    pass
```

You can also specify a custom policy:

```python
custom_policy = ErrorHandlingPolicy(
    action=ErrorAction.RETURN_DEFAULT,
    log_level=LogLevel.WARNING,
    default_value=[]
)

@handle_parser_error(policy=custom_policy)
def parse_file(file_path, file_name):
    # Parser implementation
    pass
```

#### `handle_file_operation`

Use this decorator for file operations:

```python
@handle_file_operation
def read_file(file_path):
    with open(file_path, 'r') as f:
        return f.read()
```

You can also specify a custom policy and default value:

```python
@handle_file_operation(policy=custom_policy, default_value="Default Value")
def read_file(file_path):
    with open(file_path, 'r') as f:
        return f.read()
```

### Functions

#### `safe_operation`

Use this function to execute an operation safely:

```python
result = safe_operation(
    lambda: some_operation(),
    "Operation failed",
    policy=custom_policy,
    context={"context": "information"}
)
```

#### `handle_archive_operation`

Use this function to execute archive operations safely:

```python
result = handle_archive_operation(
    lambda: extract_archive(),
    "ZIP",
    "archive.zip",
    "Failed to extract archive",
    policy=custom_policy
)
```

## Best Practices

1. **Use the appropriate policy for each type of operation**:
   - Use `PARSER_POLICY` for parsers
   - Use `FILE_OPERATION_POLICY` for file operations
   - Use `ARCHIVE_OPERATION_POLICY` for archive operations
   - Use `SEARCH_OPERATION_POLICY` for search operations

2. **Provide meaningful context information**:
   - Include file names, paths, and other relevant information
   - Use descriptive error messages

3. **Choose the appropriate log level**:
   - Use `ERROR` for critical errors
   - Use `WARNING` for non-critical errors
   - Use `INFO` for informational messages
   - Use `DEBUG` for detailed debugging information

4. **Be consistent in error handling**:
   - Use the same policy for similar operations
   - Return similar default values for similar operations
   - Raise similar exceptions for similar errors

## Custom Exceptions

The module provides several custom exceptions for different types of errors:

- `SearchToolsError`: Base exception for all SearchTools errors
- `ParserError`: For parser errors
- `FileReadError`: For file read errors
- `EncodingError`: For encoding errors
- `SearchError`: For search errors
- `PluginError`: For plugin errors
- `ArchiveError`: Base exception for archive errors
- `ArchiveExtractionError`: For archive extraction errors
- `ArchiveFormatError`: For archive format errors
- `ArchiveDependencyError`: For archive dependency errors

Use these exceptions when raising custom exceptions to provide more specific error information.
