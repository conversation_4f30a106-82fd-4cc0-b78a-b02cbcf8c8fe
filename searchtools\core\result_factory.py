"""
SearchResult Factory for SearchTools application
Provides centralized creation of SearchResult objects with consistent formatting
"""

import os
from typing import List, Optional, Union

from searchtools.core.result import SearchResult
from searchtools.utils.logging import logger


class SearchResultFactory:
    """
    Factory class for creating SearchResult objects with consistent formatting.
    Eliminates code duplication across parsers and ensures uniform result structure.
    """
    
    @staticmethod
    def create_text_result(file_name: str, file_path: str, keyword: str, 
                          line_number: int, line_content: str, 
                          full_context: Optional[str] = None) -> SearchResult:
        """
        Create a SearchResult for text-based matches.
        
        Args:
            file_name (str): Name of the file
            file_path (str): Full path to the file
            keyword (str): The matched keyword
            line_number (int): Line number where match was found
            line_content (str): Content of the line containing the match
            full_context (str, optional): Full context around the match
            
        Returns:
            SearchResult: Formatted search result
        """
        location = f"Line {line_number}"
        context = line_content.strip()
        
        # Use line content as full context if not provided
        if full_context is None:
            full_context = context
        
        return SearchResult(
            file=file_name,
            path=file_path,
            keyword=keyword,
            context=context,
            location=location,
            full_context=full_context
        )
    
    @staticmethod
    def create_csv_result(file_name: str, file_path: str, keyword: str,
                         column_name: str, row_number: int, cell_content: str,
                         full_context: Optional[str] = None) -> SearchResult:
        """
        Create a SearchResult for CSV file matches.
        
        Args:
            file_name (str): Name of the CSV file
            file_path (str): Full path to the CSV file
            keyword (str): The matched keyword
            column_name (str): Name of the column containing the match
            row_number (int): Row number where match was found
            cell_content (str): Content of the cell containing the match
            full_context (str, optional): Full context around the match
            
        Returns:
            SearchResult: Formatted search result
        """
        location = f"CSV: Col {column_name}, Row {row_number}"
        context = str(cell_content).strip()
        
        # Use cell content as full context if not provided
        if full_context is None:
            full_context = context
        
        return SearchResult(
            file=file_name,
            path=file_path,
            keyword=keyword,
            context=context,
            location=location,
            full_context=full_context
        )
    
    @staticmethod
    def create_json_result(file_name: str, file_path: str, keyword: str,
                          json_path: str, value: Union[str, int, float, bool],
                          full_context: Optional[str] = None) -> SearchResult:
        """
        Create a SearchResult for JSON file matches.
        
        Args:
            file_name (str): Name of the JSON file
            file_path (str): Full path to the JSON file
            keyword (str): The matched keyword
            json_path (str): JSON path to the matched value (e.g., "data.users[0].name")
            value: The matched value
            full_context (str, optional): Full context around the match
            
        Returns:
            SearchResult: Formatted search result
        """
        location = f"JSON: {json_path}"
        context = str(value).strip()
        
        # Use value as full context if not provided
        if full_context is None:
            full_context = context
        
        return SearchResult(
            file=file_name,
            path=file_path,
            keyword=keyword,
            context=context,
            location=location,
            full_context=full_context
        )
    
    @staticmethod
    def create_xml_result(file_name: str, file_path: str, keyword: str,
                         element_path: str, element_content: str,
                         full_context: Optional[str] = None) -> SearchResult:
        """
        Create a SearchResult for XML file matches.
        
        Args:
            file_name (str): Name of the XML file
            file_path (str): Full path to the XML file
            keyword (str): The matched keyword
            element_path (str): XPath to the matched element
            element_content (str): Content of the matched element
            full_context (str, optional): Full context around the match
            
        Returns:
            SearchResult: Formatted search result
        """
        location = f"XML: {element_path}"
        context = element_content.strip()
        
        # Use element content as full context if not provided
        if full_context is None:
            full_context = context
        
        return SearchResult(
            file=file_name,
            path=file_path,
            keyword=keyword,
            context=context,
            location=location,
            full_context=full_context
        )
    
    @staticmethod
    def create_binary_result(file_name: str, file_path: str, keyword: str,
                           offset: int, surrounding_text: str = "",
                           full_context: Optional[str] = None) -> SearchResult:
        """
        Create a SearchResult for binary file matches.
        
        Args:
            file_name (str): Name of the binary file
            file_path (str): Full path to the binary file
            keyword (str): The matched keyword
            offset (int): Byte offset where match was found
            surrounding_text (str): Text surrounding the match
            full_context (str, optional): Full context around the match
            
        Returns:
            SearchResult: Formatted search result
        """
        location = f"Binary: Offset {offset}"
        context = surrounding_text.strip() if surrounding_text else f"Match at offset {offset}"
        
        # Use surrounding text as full context if not provided
        if full_context is None:
            full_context = context
        
        return SearchResult(
            file=file_name,
            path=file_path,
            keyword=keyword,
            context=context,
            location=location,
            full_context=full_context
        )
    
    @staticmethod
    def create_archive_result(file_name: str, file_path: str, keyword: str,
                            archive_member: str, member_location: str,
                            context: str, full_context: Optional[str] = None) -> SearchResult:
        """
        Create a SearchResult for matches within archive files.
        
        Args:
            file_name (str): Name of the archive file
            file_path (str): Full path to the archive file
            keyword (str): The matched keyword
            archive_member (str): Name of the file within the archive
            member_location (str): Location within the archive member
            context (str): Context of the match
            full_context (str, optional): Full context around the match
            
        Returns:
            SearchResult: Formatted search result
        """
        location = f"Archive: {archive_member} -> {member_location}"
        
        # Use context as full context if not provided
        if full_context is None:
            full_context = context
        
        return SearchResult(
            file=file_name,
            path=file_path,
            keyword=keyword,
            context=context,
            location=location,
            full_context=full_context
        )
    
    @staticmethod
    def create_generic_result(file_name: str, file_path: str, keyword: str,
                            location_type: str, location_detail: str,
                            context: str, full_context: Optional[str] = None) -> SearchResult:
        """
        Create a generic SearchResult for custom file types or special cases.
        
        Args:
            file_name (str): Name of the file
            file_path (str): Full path to the file
            keyword (str): The matched keyword
            location_type (str): Type of location (e.g., "PDF", "DOC", "Custom")
            location_detail (str): Detailed location information
            context (str): Context of the match
            full_context (str, optional): Full context around the match
            
        Returns:
            SearchResult: Formatted search result
        """
        location = f"{location_type}: {location_detail}"
        
        # Use context as full context if not provided
        if full_context is None:
            full_context = context
        
        return SearchResult(
            file=file_name,
            path=file_path,
            keyword=keyword,
            context=context,
            location=location,
            full_context=full_context
        )
    
    @staticmethod
    def create_batch_results(file_name: str, file_path: str, 
                           matches: List[dict]) -> List[SearchResult]:
        """
        Create multiple SearchResult objects efficiently for batch processing.
        
        Args:
            file_name (str): Name of the file
            file_path (str): Full path to the file
            matches (List[dict]): List of match dictionaries containing:
                - keyword (str): The matched keyword
                - location_type (str): Type of location
                - location_detail (str): Detailed location
                - context (str): Match context
                - full_context (str, optional): Full context
                
        Returns:
            List[SearchResult]: List of formatted search results
        """
        results = []
        
        for match in matches:
            try:
                result = SearchResultFactory.create_generic_result(
                    file_name=file_name,
                    file_path=file_path,
                    keyword=match['keyword'],
                    location_type=match['location_type'],
                    location_detail=match['location_detail'],
                    context=match['context'],
                    full_context=match.get('full_context')
                )
                results.append(result)
            except KeyError as e:
                logger.warning(f"Invalid match dictionary missing key {e}: {match}")
            except Exception as e:
                logger.error(f"Error creating result for match {match}: {e}")
        
        return results


class ResultFormatter:
    """
    Utility class for formatting result data consistently.
    """
    
    @staticmethod
    def format_file_path(file_path: str, make_relative: bool = True) -> str:
        """
        Format file path for display.
        
        Args:
            file_path (str): Original file path
            make_relative (bool): Whether to make path relative to current directory
            
        Returns:
            str: Formatted file path
        """
        if make_relative:
            try:
                # Try to make path relative to current working directory
                return os.path.relpath(file_path)
            except ValueError:
                # If relative path cannot be computed, return original
                return file_path
        return file_path
    
    @staticmethod
    def truncate_context(context: str, max_length: int = 100) -> str:
        """
        Truncate context string to a maximum length.
        
        Args:
            context (str): Original context string
            max_length (int): Maximum length for the context
            
        Returns:
            str: Truncated context string
        """
        if len(context) <= max_length:
            return context
        
        # Truncate and add ellipsis
        return context[:max_length - 3] + "..."
    
    @staticmethod
    def clean_context(context: str) -> str:
        """
        Clean context string by removing extra whitespace and control characters.
        
        Args:
            context (str): Original context string
            
        Returns:
            str: Cleaned context string
        """
        # Remove control characters and normalize whitespace
        cleaned = ' '.join(context.split())
        
        # Remove any remaining control characters
        cleaned = ''.join(char for char in cleaned if ord(char) >= 32 or char in '\t\n')
        
        return cleaned.strip()
    
    @staticmethod
    def highlight_keyword(context: str, keyword: str, 
                         highlight_start: str = "**", 
                         highlight_end: str = "**") -> str:
        """
        Highlight keyword occurrences in context string.
        
        Args:
            context (str): Context string
            keyword (str): Keyword to highlight
            highlight_start (str): String to insert before keyword
            highlight_end (str): String to insert after keyword
            
        Returns:
            str: Context with highlighted keywords
        """
        import re
        
        # Escape special regex characters in keyword
        escaped_keyword = re.escape(keyword)
        
        # Create pattern for case-insensitive matching
        pattern = re.compile(escaped_keyword, re.IGNORECASE)
        
        # Replace with highlighted version
        highlighted = pattern.sub(
            lambda m: f"{highlight_start}{m.group()}{highlight_end}",
            context
        )
        
        return highlighted
