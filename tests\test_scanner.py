"""
Test script for Scanner refactoring
"""

import os
import tempfile
from searchtools.core.scanner import Scanner

def test_scanner():
    """Test the Scanner class"""
    print("Testing Scanner class...")
    
    # Create a scanner instance
    scanner = Scanner()
    
    # Create a temporary test directory with some files
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create some test files
        for i in range(5):
            with open(os.path.join(temp_dir, f"test{i}.txt"), "w") as f:
                f.write(f"This is test file {i}\n")
                if i % 2 == 0:
                    f.write("It contains the keyword hello\n")
                else:
                    f.write("It contains the keyword world\n")
        
        # Set up callbacks
        results = []
        
        def progress_callback(progress):
            print(f"Progress: {progress}%")
            
        def status_callback(status):
            print(f"Status: {status}")
            
        def result_callback(result):
            print(f"Found: {result.keyword} in {result.file}")
            results.append(result)
            
        def file_progress_callback(progress, file_name):
            print(f"File progress: {progress}% - {file_name}")
            
        scanner.progress_callback = progress_callback
        scanner.status_callback = status_callback
        scanner.result_callback = result_callback
        scanner.file_progress_callback = file_progress_callback
        
        # Start the scan
        print("\nStarting scan...")
        scanner.start_scan(temp_dir, ["hello", "world"], case_sensitive=False)
        
        # Wait for the scan to complete
        import time
        while scanner.scan_in_progress:
            time.sleep(0.1)
            
        # Print results
        print(f"\nScan complete. Found {len(scanner.results)} matches:")
        for result in scanner.results:
            print(f"  - {result.keyword} in {result.file}: {result.context}")
            
        print("\nAll tests completed.")

if __name__ == "__main__":
    test_scanner()
