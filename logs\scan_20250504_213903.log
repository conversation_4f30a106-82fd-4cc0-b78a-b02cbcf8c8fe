2025-05-04 21:39:08,445 - INFO - PyPDF2 configured. PDF parsing support enabled.
2025-05-04 21:39:08,656 - INFO - win32com configured. DOC parsing support enabled.
2025-05-04 21:39:08,674 - DEBUG - antiword not available as fallback for DOC files.
2025-05-04 21:39:09,547 - INFO - Application started
2025-05-04 21:39:09,562 - INFO - Custom styling applied
2025-05-04 21:39:09,586 - INFO - rarfile configured. RAR parsing support enabled.
2025-05-04 21:39:10,309 - INFO - py7zr configured. 7z archive parsing support enabled.
2025-05-04 21:39:10,311 - DEBUG - Registered parser: Archive Parser
2025-05-04 21:39:10,317 - DEBUG - Registered parser: CSV Parser
2025-05-04 21:39:10,323 - DEBUG - Registered parser: DOC Parser
2025-05-04 21:39:10,326 - DEBUG - Registered parser: DOCX Parser
2025-05-04 21:39:10,327 - DEBUG - Registered parser: Excel Parser
2025-05-04 21:39:10,327 - DEBUG - Registered parser: ODS Parser
2025-05-04 21:39:10,329 - DEBUG - Registered parser: PDF Parser
2025-05-04 21:39:10,332 - DEBUG - Registered parser: Text Parser
2025-05-04 21:39:10,334 - INFO - Discovered 8 parser plugins
2025-05-04 21:39:10,492 - INFO - Looking for logo at (method 1): C:\Users\<USER>\Desktop\L457\SearchTools-v1\icon.ico
2025-05-04 21:39:10,494 - INFO - Logo file found at: C:\Users\<USER>\Desktop\L457\SearchTools-v1\icon.ico
2025-05-04 21:39:11,033 - INFO - Image opened, mode: RGBA, size: (159, 31)
2025-05-04 21:39:11,036 - INFO - Resized image to 100x19 to maintain aspect ratio
2025-05-04 21:39:11,083 - INFO - Converted to PhotoImage
2025-05-04 21:39:11,113 - INFO - Bottom logo loaded successfully
2025-05-04 21:39:11,866 - INFO - UI initialized
2025-05-04 21:39:17,168 - INFO - Window closed by user
2025-05-04 21:39:17,511 - INFO - Application exited
