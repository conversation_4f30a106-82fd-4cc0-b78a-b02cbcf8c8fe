"""
Security tests for SearchTools-v1 project
"""

import os
import tempfile
import unittest
import zipfile
import shutil
from unittest.mock import patch, MagicMock

from searchtools.utils.security import (
    SecurePathValidator, ArchiveSecurityManager, SecurityError,
    PathTraversalError, ResourceExhaustionError, create_secure_temp_dir
)
from searchtools.utils.temp_manager import SecureTempDirManager
import config


class TestSecurePathValidator(unittest.TestCase):
    """Test cases for SecurePathValidator"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.validator = SecurePathValidator(strict_mode=True)
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up test fixtures"""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_valid_path(self):
        """Test validation of a valid path"""
        valid_path = "documents/test.txt"
        result = self.validator.validate_path(valid_path, self.temp_dir)
        self.assertEqual(result, valid_path)
    
    def test_path_traversal_detection(self):
        """Test detection of path traversal attempts"""
        malicious_paths = [
            "../../../etc/passwd",
            "..\\..\\windows\\system32\\config\\sam",
            "documents/../../../etc/passwd",
            "documents\\..\\..\\sensitive.txt",
            "/etc/passwd",
            "C:\\Windows\\System32\\config\\sam"
        ]
        
        for malicious_path in malicious_paths:
            with self.subTest(path=malicious_path):
                with self.assertRaises(PathTraversalError):
                    self.validator.validate_path(malicious_path, self.temp_dir)
    
    def test_blocked_extensions(self):
        """Test blocking of dangerous file extensions"""
        dangerous_files = [
            "malware.exe",
            "script.bat",
            "virus.com",
            "trojan.scr",
            "MALWARE.EXE"  # Test case insensitivity
        ]
        
        for dangerous_file in dangerous_files:
            with self.subTest(file=dangerous_file):
                with self.assertRaises(SecurityError):
                    self.validator.validate_path(dangerous_file, self.temp_dir)
    
    def test_path_length_limit(self):
        """Test path length limitations"""
        # Create a path that exceeds the maximum length
        long_path = "a" * (config.ARCHIVE_SECURITY["max_path_length"] + 1)
        
        with self.assertRaises(SecurityError):
            self.validator.validate_path(long_path, self.temp_dir)
    
    def test_directory_depth_limit(self):
        """Test directory nesting depth limitations"""
        # Create a deeply nested path
        deep_path = "/".join(["dir"] * (config.ARCHIVE_SECURITY["max_directory_depth"] + 1))
        
        with self.assertRaises(SecurityError):
            self.validator.validate_path(deep_path, self.temp_dir)


class TestArchiveSecurityManager(unittest.TestCase):
    """Test cases for ArchiveSecurityManager"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.security_manager = ArchiveSecurityManager()
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up test fixtures"""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_file_count_limit(self):
        """Test file count limitations"""
        self.security_manager.reset_counters()
        
        # Simulate extracting files up to the limit
        for i in range(config.ARCHIVE_SECURITY["max_files_per_archive"]):
            self.security_manager.validate_extraction(f"file_{i}.txt", 1000, self.temp_dir)
        
        # The next file should exceed the limit
        with self.assertRaises(ResourceExhaustionError):
            self.security_manager.validate_extraction("overflow.txt", 1000, self.temp_dir)
    
    def test_individual_file_size_limit(self):
        """Test individual file size limitations"""
        self.security_manager.reset_counters()
        
        large_file_size = config.ARCHIVE_SECURITY["max_individual_file_size"] + 1
        
        with self.assertRaises(ResourceExhaustionError):
            self.security_manager.validate_extraction("large_file.txt", large_file_size, self.temp_dir)
    
    def test_total_size_limit(self):
        """Test total extracted size limitations"""
        self.security_manager.reset_counters()
        
        # Extract files that together exceed the total size limit
        file_size = config.ARCHIVE_SECURITY["max_extracted_size"] // 2 + 1
        
        # First file should be OK
        self.security_manager.validate_extraction("file1.txt", file_size, self.temp_dir)
        
        # Second file should exceed the total limit
        with self.assertRaises(ResourceExhaustionError):
            self.security_manager.validate_extraction("file2.txt", file_size, self.temp_dir)
    
    def test_extraction_stats(self):
        """Test extraction statistics tracking"""
        self.security_manager.reset_counters()
        
        # Extract a few files
        self.security_manager.validate_extraction("file1.txt", 1000, self.temp_dir)
        self.security_manager.validate_extraction("file2.txt", 2000, self.temp_dir)
        
        stats = self.security_manager.get_extraction_stats()
        
        self.assertEqual(stats["files_extracted"], 2)
        self.assertEqual(stats["total_size"], 3000)
        self.assertGreater(stats["files_remaining"], 0)
        self.assertGreater(stats["size_remaining"], 0)


class TestSecureTempDirManager(unittest.TestCase):
    """Test cases for SecureTempDirManager"""
    
    def setUp(self):
        """Set up test fixtures"""
        # Create a separate manager for testing to avoid interfering with global instance
        self.temp_manager = SecureTempDirManager()
    
    def test_create_temp_dir(self):
        """Test temporary directory creation"""
        temp_dir = self.temp_manager.create_temp_dir(prefix="test_")
        
        self.assertTrue(os.path.exists(temp_dir))
        self.assertTrue(os.path.basename(temp_dir).startswith("test_"))
        
        # Clean up
        self.temp_manager.cleanup_temp_dir(temp_dir)
    
    def test_context_manager(self):
        """Test context manager functionality"""
        temp_dir_path = None
        
        with self.temp_manager.temp_directory(prefix="context_test_") as temp_dir:
            temp_dir_path = temp_dir
            self.assertTrue(os.path.exists(temp_dir))
            
            # Create a test file
            test_file = os.path.join(temp_dir, "test.txt")
            with open(test_file, 'w') as f:
                f.write("test content")
            
            self.assertTrue(os.path.exists(test_file))
        
        # Directory should be cleaned up after context exit
        self.assertFalse(os.path.exists(temp_dir_path))
    
    def test_cleanup_all(self):
        """Test cleanup of all managed directories"""
        # Create multiple temp directories
        temp_dirs = []
        for i in range(3):
            temp_dir = self.temp_manager.create_temp_dir(prefix=f"cleanup_test_{i}_")
            temp_dirs.append(temp_dir)
        
        # Verify they exist
        for temp_dir in temp_dirs:
            self.assertTrue(os.path.exists(temp_dir))
        
        # Clean up all
        cleaned_count = self.temp_manager.cleanup_all()
        
        self.assertEqual(cleaned_count, 3)
        
        # Verify they're gone
        for temp_dir in temp_dirs:
            self.assertFalse(os.path.exists(temp_dir))
    
    def test_max_temp_dirs_limit(self):
        """Test maximum temporary directories limit"""
        # Mock the configuration to use a small limit for testing
        with patch.object(self.temp_manager, '_max_temp_dirs', 2):
            # Create directories up to the limit
            temp_dir1 = self.temp_manager.create_temp_dir(prefix="limit_test_1_")
            temp_dir2 = self.temp_manager.create_temp_dir(prefix="limit_test_2_")
            
            # The third should raise an error
            with self.assertRaises(RuntimeError):
                self.temp_manager.create_temp_dir(prefix="limit_test_3_")
            
            # Clean up
            self.temp_manager.cleanup_temp_dir(temp_dir1)
            self.temp_manager.cleanup_temp_dir(temp_dir2)


class TestMaliciousArchiveHandling(unittest.TestCase):
    """Test cases for handling malicious archives"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
        self.test_zip_path = os.path.join(self.temp_dir, "test_malicious.zip")
    
    def tearDown(self):
        """Clean up test fixtures"""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_zip_bomb_protection(self):
        """Test protection against zip bombs (highly compressed malicious archives)"""
        # Create a zip with a large uncompressed file
        with zipfile.ZipFile(self.test_zip_path, 'w', zipfile.ZIP_DEFLATED) as zf:
            # Create a large file content (simulating a zip bomb)
            large_content = b'A' * (config.ARCHIVE_SECURITY["max_individual_file_size"] + 1)
            zf.writestr("large_file.txt", large_content)
        
        # Test that the security manager catches this
        security_manager = ArchiveSecurityManager()
        security_manager.reset_counters()
        
        with zipfile.ZipFile(self.test_zip_path, 'r') as zf:
            for member in zf.namelist():
                member_info = zf.getinfo(member)
                with self.assertRaises(ResourceExhaustionError):
                    security_manager.validate_extraction(
                        member, member_info.file_size, self.temp_dir
                    )
    
    def test_path_traversal_in_zip(self):
        """Test protection against path traversal in zip files"""
        # Create a zip with malicious paths
        with zipfile.ZipFile(self.test_zip_path, 'w') as zf:
            zf.writestr("../../../etc/passwd", "malicious content")
            zf.writestr("..\\..\\windows\\system32\\config\\sam", "malicious content")
        
        security_manager = ArchiveSecurityManager()
        security_manager.reset_counters()
        
        with zipfile.ZipFile(self.test_zip_path, 'r') as zf:
            for member in zf.namelist():
                member_info = zf.getinfo(member)
                with self.assertRaises(PathTraversalError):
                    security_manager.validate_extraction(
                        member, member_info.file_size, self.temp_dir
                    )


class TestSecurityIntegration(unittest.TestCase):
    """Integration tests for security features"""

    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
        self.test_archive_path = os.path.join(self.temp_dir, "test_secure.zip")

    def tearDown(self):
        """Clean up test fixtures"""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)

    def test_secure_archive_extraction_workflow(self):
        """Test the complete secure archive extraction workflow"""
        # Create a test archive with various file types
        with zipfile.ZipFile(self.test_archive_path, 'w') as zf:
            zf.writestr("safe_document.txt", "This is safe content")
            zf.writestr("data/report.csv", "col1,col2\nvalue1,value2")
            zf.writestr("images/photo.jpg", b"fake image data")

        # Mock the scanner callback
        scanner_callback = MagicMock()
        temp_dirs = []

        # Import and test the actual archive parser
        from searchtools.parsers.archive_parser import ArchiveParser

        # Test that the archive is processed securely
        result = ArchiveParser.parse_zip(
            self.test_archive_path,
            "test_secure.zip",
            scanner_callback,
            temp_dirs
        )

        self.assertTrue(result)
        self.assertEqual(len(temp_dirs), 1)
        scanner_callback.assert_called_once()

        # Verify the temp directory was created and contains expected files
        extracted_dir = temp_dirs[0]
        self.assertTrue(os.path.exists(extracted_dir))

        # Check that files were extracted safely
        expected_files = [
            "safe_document.txt",
            "data/report.csv",
            "images/photo.jpg"
        ]

        for expected_file in expected_files:
            file_path = os.path.join(extracted_dir, expected_file)
            self.assertTrue(os.path.exists(file_path), f"Expected file not found: {expected_file}")

    def test_malicious_archive_rejection(self):
        """Test that malicious archives are properly rejected"""
        # Create a malicious archive with path traversal
        with zipfile.ZipFile(self.test_archive_path, 'w') as zf:
            zf.writestr("safe_file.txt", "safe content")
            zf.writestr("../../../malicious.txt", "malicious content")

        scanner_callback = MagicMock()
        temp_dirs = []

        from searchtools.parsers.archive_parser import ArchiveParser

        # The archive should still be processed, but malicious files should be skipped
        result = ArchiveParser.parse_zip(
            self.test_archive_path,
            "malicious.zip",
            scanner_callback,
            temp_dirs
        )

        self.assertTrue(result)  # Processing should complete

        # Verify only safe files were extracted
        if temp_dirs:
            extracted_dir = temp_dirs[0]
            safe_file = os.path.join(extracted_dir, "safe_file.txt")
            malicious_file = os.path.join(extracted_dir, "../../../malicious.txt")

            self.assertTrue(os.path.exists(safe_file))
            self.assertFalse(os.path.exists(malicious_file))


if __name__ == '__main__':
    unittest.main()
