2025-05-04 21:36:30,543 - INFO - PyPDF2 configured. PDF parsing support enabled.
2025-05-04 21:36:30,819 - INFO - win32com configured. DOC parsing support enabled.
2025-05-04 21:36:30,828 - DEBUG - antiword not available as fallback for DOC files.
2025-05-04 21:36:32,690 - INFO - Application started
2025-05-04 21:36:32,719 - INFO - Custom styling applied
2025-05-04 21:36:33,044 - INFO - rarfile configured. RAR parsing support enabled.
2025-05-04 21:36:35,872 - INFO - py7zr configured. 7z archive parsing support enabled.
2025-05-04 21:36:35,875 - DEBUG - Registered parser: Archive Parser
2025-05-04 21:36:35,879 - DEBUG - Registered parser: CSV Parser
2025-05-04 21:36:35,891 - DEBUG - Registered parser: DOC Parser
2025-05-04 21:36:35,904 - DEBUG - Registered parser: DOCX Parser
2025-05-04 21:36:35,912 - DEBUG - Registered parser: Excel Parser
2025-05-04 21:36:35,934 - DEBUG - Registered parser: ODS Parser
2025-05-04 21:36:35,946 - DEBUG - Registered parser: PDF Parser
2025-05-04 21:36:35,959 - DEBUG - Registered parser: Text Parser
2025-05-04 21:36:35,961 - INFO - Discovered 8 parser plugins
2025-05-04 21:36:36,532 - INFO - Looking for logo at (method 1): C:\Users\<USER>\Desktop\L457\SearchTools-v1\icon.ico
2025-05-04 21:36:36,537 - INFO - Logo file found at: C:\Users\<USER>\Desktop\L457\SearchTools-v1\icon.ico
2025-05-04 21:36:38,573 - INFO - Image opened, mode: RGBA, size: (159, 31)
2025-05-04 21:36:38,589 - INFO - Resized image to 100x19 to maintain aspect ratio
2025-05-04 21:36:38,725 - INFO - Converted to PhotoImage
2025-05-04 21:36:39,054 - INFO - Bottom logo loaded successfully
2025-05-04 21:36:41,375 - INFO - UI initialized
2025-05-04 21:38:05,338 - INFO - Window closed by user
2025-05-04 21:38:06,009 - INFO - Application exited
