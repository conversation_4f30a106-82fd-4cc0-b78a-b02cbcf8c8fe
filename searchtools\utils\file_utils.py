"""
File handling utilities
"""

import os
import shutil
import tempfile
from typing import List, <PERSON>ple

from searchtools.utils.logging import logger
from searchtools.utils.encoding import detect_encoding, read_binary_fallback
from searchtools.utils.error_handling import handle_file_operation, FileReadError, EncodingError

def safe_read_text(file_path: str) -> List[Tuple[int, str]]:
    """
    Read text files completely without size limitations.

    Args:
        file_path (str): Path to the file

    Returns:
        list: List of tuples (line_number, line_text)

    Raises:
        FileReadError: If the file cannot be read
        EncodingError: If the file's encoding cannot be determined or used
    """
    try:
        # Process all content without size limitations
        encoding = detect_encoding(file_path)
        lines = []
        with open(file_path, 'r', encoding=encoding, errors='replace') as f:
            for i, line in enumerate(f):
                # No limit on number of lines
                lines.append((i + 1, line))

        return lines
    except UnicodeDecodeError:
        # Fallback to binary mode
        logger.warning(f"Unicode decode error for {os.path.basename(file_path)}, trying binary")
        try:
            return read_binary_fallback(file_path)
        except Exception as e:
            logger.error(f"Binary fallback failed: {e}")
            raise EncodingError(f"Failed to decode file {os.path.basename(file_path)}: {e}") from e
    except (FileNotFoundError, PermissionError) as e:
        logger.error(f"File access error for {os.path.basename(file_path)}: {e}")
        raise FileReadError(f"Cannot access file {os.path.basename(file_path)}: {e}") from e
    except Exception as e:
        logger.error(f"Text reading error for {os.path.basename(file_path)}: {e}")
        raise FileReadError(f"Error reading file {os.path.basename(file_path)}: {e}") from e

@handle_file_operation
def create_temp_dir(prefix: str = "searchtools_") -> str:
    """
    Create a temporary directory.

    Args:
        prefix (str): Prefix for the directory name

    Returns:
        str: Path to the temporary directory

    Raises:
        SearchToolsError: If the directory cannot be created
    """
    return tempfile.mkdtemp(prefix=prefix)

def cleanup_temp_dir(temp_dir: str) -> None:
    """
    Remove a temporary directory.

    Args:
        temp_dir (str): Path to the temporary directory
    """
    from searchtools.utils.error_handling import safe_operation

    if not os.path.exists(temp_dir):
        return

    def _remove_dir():
        shutil.rmtree(temp_dir)
        logger.info(f"Removed temporary directory: {temp_dir}")

    from searchtools.utils.error_handling import ErrorHandlingPolicy, ErrorAction, LogLevel

    # Create a policy for directory removal
    policy = ErrorHandlingPolicy(
        action=ErrorAction.LOG_AND_CONTINUE,
        log_level=LogLevel.ERROR,
        include_traceback=True
    )

    safe_operation(
        _remove_dir,
        f"Error removing temporary directory {temp_dir}",
        policy=policy
    )

def get_file_extension(file_path: str) -> str:
    """
    Get the file extension from a path.

    Args:
        file_path (str): Path to the file

    Returns:
        str: File extension in lowercase
    """
    return os.path.splitext(file_path)[1].lower()
