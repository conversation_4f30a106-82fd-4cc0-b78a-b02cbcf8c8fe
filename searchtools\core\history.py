"""
Search history management
"""

import os
import json
from typing import List, Dict, Any, Optional
from datetime import datetime

from searchtools.utils.logging import logger
import config

class SearchHistory:
    """
    Class for managing search history.
    """
    
    def __init__(self, history_file: Optional[str] = None):
        """
        Initialize the search history.
        
        Args:
            history_file (str, optional): Path to the history file. Defaults to None.
        """
        self.history_file = history_file or os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'search_history.json')
        self.history: List[Dict[str, Any]] = []
        self.load_history()
    
    def load_history(self) -> None:
        """
        Load search history from file.
        """
        try:
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r') as f:
                    self.history = json.load(f)
                logger.info(f"Loaded {len(self.history)} search history entries")
            else:
                logger.info("No search history file found. Starting with empty history.")
                self.history = []
        except Exception as e:
            logger.error(f"Error loading search history: {e}")
            self.history = []
    
    def save_history(self) -> None:
        """
        Save search history to file.
        """
        try:
            # Ensure we don't exceed the maximum history size
            if len(self.history) > config.MAX_HISTORY_ENTRIES:
                self.history = self.history[-config.MAX_HISTORY_ENTRIES:]
                
            with open(self.history_file, 'w') as f:
                json.dump(self.history, f, indent=2)
            logger.info(f"Saved {len(self.history)} search history entries")
        except Exception as e:
            logger.error(f"Error saving search history: {e}")
    
    def add_search(self, folder_path: str, keywords: List[str], case_sensitive: bool = False) -> None:
        """
        Add a search to the history.
        
        Args:
            folder_path (str): Root folder path
            keywords (List[str]): List of keywords
            case_sensitive (bool, optional): Whether the search was case-sensitive. Defaults to False.
        """
        # Create a new history entry
        entry = {
            'timestamp': datetime.now().isoformat(),
            'folder_path': folder_path,
            'keywords': keywords,
            'case_sensitive': case_sensitive
        }
        
        # Check if this exact search already exists
        for existing in self.history:
            if (existing.get('folder_path') == folder_path and 
                existing.get('keywords') == keywords and 
                existing.get('case_sensitive') == case_sensitive):
                # Remove the existing entry (we'll add it again at the end)
                self.history.remove(existing)
                break
        
        # Add the new entry at the end (most recent)
        self.history.append(entry)
        
        # Save the updated history
        self.save_history()
    
    def get_history(self) -> List[Dict[str, Any]]:
        """
        Get the search history.
        
        Returns:
            List[Dict[str, Any]]: List of search history entries
        """
        return self.history
    
    def clear_history(self) -> None:
        """
        Clear the search history.
        """
        self.history = []
        self.save_history()
        logger.info("Search history cleared")
    
    def get_formatted_history(self) -> List[str]:
        """
        Get a formatted list of search history entries for display.
        
        Returns:
            List[str]: List of formatted history entries
        """
        formatted = []
        for entry in reversed(self.history):  # Most recent first
            timestamp = datetime.fromisoformat(entry['timestamp'])
            formatted_date = timestamp.strftime('%Y-%m-%d %H:%M:%S')
            keywords_str = ', '.join(entry['keywords'])
            case_str = "Case Sensitive" if entry.get('case_sensitive', False) else "Case Insensitive"
            formatted.append(f"{formatted_date} - {keywords_str} - {case_str} - {entry['folder_path']}")
        return formatted

# Create a singleton instance
search_history = SearchHistory()
