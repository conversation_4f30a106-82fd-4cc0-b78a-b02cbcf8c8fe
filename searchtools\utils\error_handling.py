"""
Error handling utilities for SearchTools.

This module provides standardized error handling functions and custom exceptions
for the SearchTools project.
"""

import functools
import traceback
import zipfile
import enum
from typing import Any, Callable, Optional, Type, TypeVar, Union, Dict

from searchtools.utils.logging import logger

# Type variables for generic functions
T = TypeVar('T')
R = TypeVar('R')

# Error handling policy enum
class ErrorAction(enum.Enum):
    """
    Defines the action to take when an error occurs.
    """
    # Return a default value and log the error
    RETURN_DEFAULT = "return_default"
    # Raise the original exception
    RAISE_ORIGINAL = "raise_original"
    # Raise a custom exception
    RAISE_CUSTOM = "raise_custom"
    # Log and continue (for non-critical errors)
    LOG_AND_CONTINUE = "log_and_continue"
    # Retry the operation
    RETRY = "retry"

class LogLevel(enum.Enum):
    """
    Defines the log level to use when logging errors.
    """
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"
    DEBUG = "debug"

class ErrorHandlingPolicy:
    """
    Defines a policy for handling errors in a consistent way.

    This class provides a standardized approach to error handling across
    the application, ensuring that similar errors are handled consistently.
    """

    def __init__(
        self,
        action: ErrorAction = ErrorAction.RETURN_DEFAULT,
        log_level: LogLevel = LogLevel.ERROR,
        default_value: Any = None,
        custom_exception_type: Optional[Type[Exception]] = None,
        max_retries: int = 0,
        retry_delay: float = 0.0,
        include_traceback: bool = True
    ):
        """
        Initialize the error handling policy.

        Args:
            action: The action to take when an error occurs
            log_level: The log level to use when logging errors
            default_value: The default value to return if action is RETURN_DEFAULT
            custom_exception_type: The exception type to raise if action is RAISE_CUSTOM
            max_retries: Maximum number of retries if action is RETRY
            retry_delay: Delay between retries in seconds
            include_traceback: Whether to include the traceback in log messages
        """
        self.action = action
        self.log_level = log_level
        self.default_value = default_value
        self.custom_exception_type = custom_exception_type
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.include_traceback = include_traceback

    def handle_error(
        self,
        error: Exception,
        context: Dict[str, Any] = None,
        error_message: Optional[str] = None
    ) -> Any:
        """
        Handle an error according to the policy.

        Args:
            error: The exception that occurred
            context: Additional context information for logging
            error_message: Custom error message to use instead of the exception message

        Returns:
            The result based on the policy action

        Raises:
            Exception: If the policy action is to raise an exception
        """
        # Log the error
        self._log_error(error, context, error_message)

        # Take action based on the policy
        if self.action == ErrorAction.RETURN_DEFAULT:
            return self.default_value
        elif self.action == ErrorAction.RAISE_ORIGINAL:
            raise error
        elif self.action == ErrorAction.RAISE_CUSTOM:
            if self.custom_exception_type:
                raise self.custom_exception_type(error_message or str(error)) from error
            else:
                # Fallback to original if no custom type specified
                raise error
        elif self.action == ErrorAction.LOG_AND_CONTINUE:
            return None
        elif self.action == ErrorAction.RETRY:
            # Retry logic would be implemented here
            # For now, just return default value
            return self.default_value
        else:
            # Unknown action, log and raise
            logger.error(f"Unknown error handling action: {self.action}")
            raise error

    def _log_error(
        self,
        error: Exception,
        context: Optional[Dict[str, Any]] = None,
        error_message: Optional[str] = None
    ) -> None:
        """
        Log an error according to the policy.

        Args:
            error: The exception that occurred
            context: Additional context information for logging
            error_message: Custom error message to use instead of the exception message
        """
        # Format the message
        message = error_message or str(error)
        if context:
            context_str = ", ".join(f"{k}={v}" for k, v in context.items())
            message = f"{message} [{context_str}]"

        # Log at the appropriate level
        if self.log_level == LogLevel.ERROR:
            logger.error(message)
        elif self.log_level == LogLevel.WARNING:
            logger.warning(message)
        elif self.log_level == LogLevel.INFO:
            logger.info(message)
        elif self.log_level == LogLevel.DEBUG:
            logger.debug(message)

        # Log traceback if requested
        if self.include_traceback:
            logger.debug(f"Traceback: {traceback.format_exc()}")

# Predefined policies for common scenarios
PARSER_POLICY = ErrorHandlingPolicy(
    action=ErrorAction.RETURN_DEFAULT,
    log_level=LogLevel.ERROR,
    default_value=[],  # Empty list for parsers
    include_traceback=True
)

FILE_OPERATION_POLICY = ErrorHandlingPolicy(
    action=ErrorAction.RAISE_CUSTOM,
    log_level=LogLevel.ERROR,
    include_traceback=True
)

ARCHIVE_OPERATION_POLICY = ErrorHandlingPolicy(
    action=ErrorAction.RAISE_CUSTOM,
    log_level=LogLevel.ERROR,
    include_traceback=True
)

SEARCH_OPERATION_POLICY = ErrorHandlingPolicy(
    action=ErrorAction.RETURN_DEFAULT,
    log_level=LogLevel.WARNING,
    default_value=None,
    include_traceback=False
)

# UI-specific policy that logs errors but continues execution
UI_OPERATION_POLICY = ErrorHandlingPolicy(
    action=ErrorAction.LOG_AND_CONTINUE,
    log_level=LogLevel.ERROR,
    include_traceback=True
)

# CLI-specific policy that logs errors and returns an exit code
CLI_OPERATION_POLICY = ErrorHandlingPolicy(
    action=ErrorAction.RETURN_DEFAULT,
    log_level=LogLevel.ERROR,
    default_value=1,  # Non-zero exit code indicates error
    include_traceback=True
)

# Custom exceptions
class SearchToolsError(Exception):
    """Base exception for all SearchTools errors."""
    pass

class ParserError(SearchToolsError):
    """Exception raised when a parser fails to parse a file."""
    pass

class FileReadError(SearchToolsError):
    """Exception raised when a file cannot be read."""
    pass

class EncodingError(SearchToolsError):
    """Exception raised when a file's encoding cannot be determined or used."""
    pass

class SearchError(SearchToolsError):
    """Exception raised when a search operation fails."""
    pass

class PluginError(SearchToolsError):
    """Exception raised when a plugin operation fails."""
    pass

class ArchiveError(SearchToolsError):
    """Base exception for archive-related errors."""
    pass

class ArchiveExtractionError(ArchiveError):
    """Exception raised when an archive cannot be extracted."""
    pass

class ArchiveFormatError(ArchiveError):
    """Exception raised when an archive has an invalid format."""
    pass

class ArchiveDependencyError(ArchiveError):
    """Exception raised when a required dependency for archive handling is missing."""
    pass

# Error handling functions
def handle_parser_error(
    func: Optional[Callable[..., R]] = None,
    *,
    policy: Optional[ErrorHandlingPolicy] = None
) -> Callable[..., R]:
    """
    Decorator for handling parser errors.

    This decorator catches exceptions in parser methods, logs them appropriately,
    and handles them according to the specified policy.

    Args:
        func: The function to decorate
        policy: The error handling policy to use (defaults to PARSER_POLICY)

    Returns:
        The decorated function
    """
    # Support both @handle_parser_error and @handle_parser_error(policy=...)
    if func is None:
        return lambda f: handle_parser_error(f, policy=policy)

    # Use the default parser policy if none is provided
    effective_policy = policy or PARSER_POLICY

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            # Get context information for logging
            file_name = kwargs.get('file_name', 'unknown')
            context = {'file_name': file_name}

            # Create an appropriate error message
            error_message = f"Parser error for {file_name}"

            # Handle the error according to the policy
            return effective_policy.handle_error(e, context, error_message)
    return wrapper

def handle_file_operation(
    func: Optional[Callable[..., T]] = None,
    *,
    policy: Optional[ErrorHandlingPolicy] = None,
    default_value: Optional[T] = None
) -> Callable[..., T]:
    """
    Decorator for handling file operations.

    This decorator catches exceptions in file operation methods, logs them appropriately,
    and handles them according to the specified policy.

    Args:
        func: The function to decorate
        policy: The error handling policy to use (defaults to FILE_OPERATION_POLICY)
        default_value: The default value to return on error (overrides policy default)

    Returns:
        The decorated function
    """
    # Support both @handle_file_operation and @handle_file_operation(policy=...)
    if func is None:
        return lambda f: handle_file_operation(f, policy=policy, default_value=default_value)

    # Use the default file operation policy if none is provided
    effective_policy = policy or FILE_OPERATION_POLICY

    # Create a copy of the policy with the custom default value if provided
    if default_value is not None and effective_policy.action == ErrorAction.RETURN_DEFAULT:
        effective_policy = ErrorHandlingPolicy(
            action=effective_policy.action,
            log_level=effective_policy.log_level,
            default_value=default_value,
            custom_exception_type=effective_policy.custom_exception_type,
            max_retries=effective_policy.max_retries,
            retry_delay=effective_policy.retry_delay,
            include_traceback=effective_policy.include_traceback
        )

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except (FileNotFoundError, PermissionError) as e:
            # Get context information for logging
            file_path = kwargs.get('file_path', args[0] if args else 'unknown')
            context = {'file_path': file_path, 'error_type': 'access_error'}

            # Create an appropriate error message
            error_message = f"Cannot access file {file_path}"

            # Use a custom exception type for file access errors
            custom_policy = ErrorHandlingPolicy(
                action=effective_policy.action,
                log_level=effective_policy.log_level,
                default_value=effective_policy.default_value,
                custom_exception_type=FileReadError,
                max_retries=effective_policy.max_retries,
                retry_delay=effective_policy.retry_delay,
                include_traceback=effective_policy.include_traceback
            )

            # Handle the error according to the policy
            return custom_policy.handle_error(e, context, error_message)

        except UnicodeDecodeError as e:
            # Get context information for logging
            file_path = kwargs.get('file_path', args[0] if args else 'unknown')
            context = {'file_path': file_path, 'error_type': 'encoding_error'}

            # Create an appropriate error message
            error_message = f"Cannot decode file {file_path}"

            # Use a custom exception type for encoding errors
            custom_policy = ErrorHandlingPolicy(
                action=effective_policy.action,
                log_level=effective_policy.log_level,
                default_value=effective_policy.default_value,
                custom_exception_type=EncodingError,
                max_retries=effective_policy.max_retries,
                retry_delay=effective_policy.retry_delay,
                include_traceback=effective_policy.include_traceback
            )

            # Handle the error according to the policy
            return custom_policy.handle_error(e, context, error_message)

        except Exception as e:
            # Get context information for logging
            file_path = kwargs.get('file_path', args[0] if args else 'unknown')
            context = {'file_path': file_path, 'error_type': 'general_error'}

            # Create an appropriate error message
            error_message = f"Error processing file {file_path}"

            # Handle the error according to the policy
            return effective_policy.handle_error(e, context, error_message)
    return wrapper

def safe_operation(
    operation: Callable[..., T],
    error_message: str,
    policy: Optional[ErrorHandlingPolicy] = None,
    context: Optional[Dict[str, Any]] = None,
    exception_types: Optional[Union[Type[Exception], tuple[Type[Exception], ...]]] = None
) -> T:
    """
    Execute an operation safely, handling exceptions according to the specified policy.

    Args:
        operation: The operation to execute
        error_message: The error message to log
        policy: The error handling policy to use (defaults to a policy that returns None)
        context: Additional context information for logging
        exception_types: The exception types to catch (defaults to Exception)

    Returns:
        The result of the operation or a value determined by the policy
    """
    # Create a default policy if none is provided
    if policy is None:
        policy = ErrorHandlingPolicy(
            action=ErrorAction.RETURN_DEFAULT,
            log_level=LogLevel.ERROR,
            default_value=None,
            include_traceback=True
        )

    # Use a default empty context if none is provided
    context = context or {}

    # Determine which exception types to catch
    exception_types = exception_types or Exception

    try:
        return operation()
    except exception_types as e:
        # Handle the error according to the policy
        return policy.handle_error(e, context, error_message)



def handle_archive_operation(
    operation: Callable[..., T],
    archive_type: str,
    file_name: str,
    error_message: str = "Archive operation failed",
    policy: Optional[ErrorHandlingPolicy] = None
) -> Optional[T]:
    """
    Execute an archive operation safely, handling exceptions according to the specified policy.

    Args:
        operation: The operation to execute
        archive_type: The type of archive (e.g., 'ZIP', 'RAR', '7z')
        file_name: The name of the archive file
        error_message: The base error message to log
        policy: The error handling policy to use (defaults to ARCHIVE_OPERATION_POLICY)

    Returns:
        The result of the operation or a value determined by the policy

    Raises:
        ArchiveFormatError: If the archive format is invalid and policy specifies to raise
        ArchiveDependencyError: If a required dependency is missing and policy specifies to raise
        ArchiveExtractionError: If the archive cannot be extracted and policy specifies to raise
    """
    # Use the default archive operation policy if none is provided
    policy = policy or ARCHIVE_OPERATION_POLICY

    # Common context information
    context = {
        'archive_type': archive_type,
        'file_name': file_name
    }

    try:
        return operation()
    except ImportError as e:
        # Create a custom policy for dependency errors
        custom_policy = ErrorHandlingPolicy(
            action=policy.action,
            log_level=policy.log_level,
            default_value=policy.default_value,
            custom_exception_type=ArchiveDependencyError,
            max_retries=policy.max_retries,
            retry_delay=policy.retry_delay,
            include_traceback=policy.include_traceback
        )

        # Create an appropriate error message
        custom_error_message = f"Missing dependency for {archive_type} archive"

        # Handle the error according to the policy
        return custom_policy.handle_error(e, context, custom_error_message)

    except (zipfile.BadZipFile, ValueError) as e:
        # Create a custom policy for format errors
        custom_policy = ErrorHandlingPolicy(
            action=policy.action,
            log_level=policy.log_level,
            default_value=policy.default_value,
            custom_exception_type=ArchiveFormatError,
            max_retries=policy.max_retries,
            retry_delay=policy.retry_delay,
            include_traceback=policy.include_traceback
        )

        # Create an appropriate error message
        custom_error_message = f"Invalid {archive_type} format"

        # Handle the error according to the policy
        return custom_policy.handle_error(e, context, custom_error_message)

    except (FileNotFoundError, PermissionError) as e:
        # Create a custom policy for file access errors
        custom_policy = ErrorHandlingPolicy(
            action=policy.action,
            log_level=policy.log_level,
            default_value=policy.default_value,
            custom_exception_type=FileReadError,
            max_retries=policy.max_retries,
            retry_delay=policy.retry_delay,
            include_traceback=policy.include_traceback
        )

        # Create an appropriate error message
        error_type = "not found" if isinstance(e, FileNotFoundError) else "permission denied"
        custom_error_message = f"Archive file {error_type}"

        # Handle the error according to the policy
        return custom_policy.handle_error(e, context, custom_error_message)

    except Exception as e:
        # Create a custom policy for general extraction errors
        custom_policy = ErrorHandlingPolicy(
            action=policy.action,
            log_level=policy.log_level,
            default_value=policy.default_value,
            custom_exception_type=ArchiveExtractionError,
            max_retries=policy.max_retries,
            retry_delay=policy.retry_delay,
            include_traceback=policy.include_traceback
        )

        # Create an appropriate error message
        custom_error_message = f"{error_message} for {archive_type} file {file_name}"

        # Handle the error according to the policy
        return custom_policy.handle_error(e, context, custom_error_message)

# Type variable for UI operations
U = TypeVar('U')

def handle_ui_operation(
    func: Optional[Callable[..., U]] = None,
    *,
    policy: Optional[ErrorHandlingPolicy] = None,
    error_message: Optional[str] = None
) -> Callable[..., U]:
    """
    Decorator for handling UI operations.

    This decorator catches exceptions in UI methods, logs them appropriately,
    and handles them according to the specified policy.

    Args:
        func: The function to decorate
        policy: The error handling policy to use (defaults to UI_OPERATION_POLICY)
        error_message: Custom error message to use (defaults to function name)

    Returns:
        The decorated function
    """
    # Support both @handle_ui_operation and @handle_ui_operation(policy=...)
    if func is None:
        return lambda f: handle_ui_operation(f, policy=policy, error_message=error_message)

    # Use the default UI policy if none is provided
    effective_policy = policy or UI_OPERATION_POLICY

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            # Create context with function name and args
            context = {
                'function': func.__name__,
                'args_count': len(args),
                'kwargs_keys': list(kwargs.keys())
            }

            # Create an appropriate error message
            message = error_message or f"UI operation error in {func.__name__}"

            # Handle the error according to the policy
            return effective_policy.handle_error(e, context, message)
    return wrapper

# Type variable for CLI operations
C = TypeVar('C')

def handle_cli_operation(
    func: Optional[Callable[..., C]] = None,
    *,
    policy: Optional[ErrorHandlingPolicy] = None,
    error_message: Optional[str] = None,
    exit_on_error: bool = False
) -> Callable[..., C]:
    """
    Decorator for handling CLI operations.

    This decorator catches exceptions in CLI methods, logs them appropriately,
    and handles them according to the specified policy.

    Args:
        func: The function to decorate
        policy: The error handling policy to use (defaults to CLI_OPERATION_POLICY)
        error_message: Custom error message to use (defaults to function name)
        exit_on_error: Whether to exit the program on error (defaults to False)

    Returns:
        The decorated function
    """
    # Support both @handle_cli_operation and @handle_cli_operation(policy=...)
    if func is None:
        return lambda f: handle_cli_operation(f, policy=policy, error_message=error_message, exit_on_error=exit_on_error)

    # Use the default CLI policy if none is provided
    effective_policy = policy or CLI_OPERATION_POLICY

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            # Create context with function name and args
            context = {
                'function': func.__name__,
                'args_count': len(args),
                'kwargs_keys': list(kwargs.keys())
            }

            # Create an appropriate error message
            message = error_message or f"CLI operation error in {func.__name__}"

            # Print the error message to stderr
            import sys
            print(f"Error: {message}", file=sys.stderr)

            # Handle the error according to the policy
            result = effective_policy.handle_error(e, context, message)

            # Exit if requested
            if exit_on_error:
                exit_code = 1 if result is None else result
                sys.exit(exit_code)

            return result
    return wrapper