2025-05-04 16:16:32,360 - INFO - PyPDF2 configured. PDF parsing support enabled.
2025-05-04 16:16:32,393 - INFO - win32com configured. DOC parsing support enabled.
2025-05-04 16:16:32,404 - DEBUG - antiword not available as fallback for DOC files.
2025-05-04 16:16:32,755 - INFO - Application started
2025-05-04 16:16:32,758 - INFO - Custom styling applied
2025-05-04 16:16:32,770 - INFO - rarfile configured. RAR parsing support enabled.
2025-05-04 16:16:33,129 - INFO - py7zr configured. 7z archive parsing support enabled.
2025-05-04 16:16:33,131 - DEBUG - Registered parser: Archive Parser
2025-05-04 16:16:33,132 - DEBUG - Registered parser: CSV Parser
2025-05-04 16:16:33,132 - DEBUG - Registered parser: DOC Parser
2025-05-04 16:16:33,132 - DEBUG - Registered parser: DOCX Parser
2025-05-04 16:16:33,133 - DEBUG - Registered parser: Excel Parser
2025-05-04 16:16:33,133 - DEBUG - Registered parser: ODS Parser
2025-05-04 16:16:33,133 - DEBUG - Registered parser: PDF Parser
2025-05-04 16:16:33,133 - DEBUG - Registered parser: Text Parser
2025-05-04 16:16:33,133 - INFO - Discovered 8 parser plugins
2025-05-04 16:16:33,201 - INFO - Looking for logo at (method 1): C:\Users\<USER>\Desktop\L457\SearchTools-v1\icon.ico
2025-05-04 16:16:33,202 - INFO - Logo file found at: C:\Users\<USER>\Desktop\L457\SearchTools-v1\icon.ico
2025-05-04 16:16:33,359 - INFO - Image opened, mode: RGBA, size: (159, 31)
2025-05-04 16:16:33,363 - INFO - Resized image to 100x19 to maintain aspect ratio
2025-05-04 16:16:33,385 - INFO - Converted to PhotoImage
2025-05-04 16:16:33,413 - INFO - Bottom logo loaded successfully
2025-05-04 16:16:33,788 - INFO - UI initialized
2025-05-04 16:16:36,303 - INFO - Window closed by user
2025-05-04 16:16:36,560 - INFO - Application exited
