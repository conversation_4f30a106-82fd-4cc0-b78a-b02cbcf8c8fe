2025-05-05 09:21:59,063 - INFO - PyPDF2 configured. PDF parsing support enabled.
2025-05-05 09:22:01,635 - INFO - win32com configured. DOC parsing support enabled.
2025-05-05 09:22:01,665 - DEBUG - antiword not available as fallback for DOC files.
2025-05-05 09:22:08,360 - INFO - Application started
2025-05-05 09:22:08,365 - INFO - Custom styling applied
2025-05-05 09:22:08,380 - INFO - rarfile configured. RAR parsing support enabled.
2025-05-05 09:22:12,191 - INFO - py7zr configured. 7z archive parsing support enabled.
2025-05-05 09:22:12,196 - DEBUG - Registered parser: Archive Parser
2025-05-05 09:22:12,198 - DEBUG - Registered parser: CSV Parser
2025-05-05 09:22:12,198 - DEBUG - Registered parser: DOC Parser
2025-05-05 09:22:12,198 - DEBUG - Registered parser: DOCX Parser
2025-05-05 09:22:12,200 - DEBUG - Registered parser: Excel Parser
2025-05-05 09:22:12,200 - DEBUG - Registered parser: ODS Parser
2025-05-05 09:22:12,200 - DEBUG - Registered parser: PDF Parser
2025-05-05 09:22:12,200 - DEBUG - Registered parser: Text Parser
2025-05-05 09:22:12,202 - INFO - Discovered 8 parser plugins
2025-05-05 09:22:12,429 - INFO - Looking for logo at (method 1): c:\Users\<USER>\Desktop\L457\SearchTools-v1\icon.ico
2025-05-05 09:22:12,430 - INFO - Logo file found at: c:\Users\<USER>\Desktop\L457\SearchTools-v1\icon.ico
2025-05-05 09:22:13,934 - INFO - Image opened, mode: RGBA, size: (159, 31)
2025-05-05 09:22:13,993 - INFO - Resized image to 100x19 to maintain aspect ratio
2025-05-05 09:22:14,615 - INFO - Converted to PhotoImage
2025-05-05 09:22:14,962 - INFO - Bottom logo loaded successfully
2025-05-05 09:22:20,434 - INFO - UI initialized
2025-05-05 09:24:49,803 - INFO - Window closed by user
2025-05-05 09:24:51,073 - INFO - Application exited
