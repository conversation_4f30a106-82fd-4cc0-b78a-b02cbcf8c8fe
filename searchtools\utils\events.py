"""
Event system for decoupling components.
"""

from enum import Enum, auto
from typing import Any, Callable, Dict, List, Optional
import threading

class EventType(Enum):
    """
    Types of events that can be dispatched.
    """
    PROGRESS_UPDATE = auto()
    STATUS_UPDATE = auto()
    RESULT_FOUND = auto()
    FILE_PROGRESS_UPDATE = auto()
    SCAN_STARTED = auto()
    SCAN_COMPLETED = auto()
    SCAN_STOPPED = auto()
    ERROR_OCCURRED = auto()

class Event:
    """
    Base class for all events.
    """
    def __init__(self, event_type: EventType, data: Optional[Dict[str, Any]] = None):
        """
        Initialize an event.

        Args:
            event_type (EventType): Type of the event
            data (dict, optional): Event data. Defaults to None.
        """
        self.event_type = event_type
        self.data = data or {}

class EventDispatcher:
    """
    Event dispatcher for publishing and subscribing to events.
    """
    def __init__(self):
        """
        Initialize the event dispatcher.
        """
        self._subscribers: Dict[EventType, List[Callable[[Event], None]]] = {}
        self._lock = threading.Lock()

    def subscribe(self, event_type: EventType, callback: Callable[[Event], None]) -> None:
        """
        Subscribe to an event type.

        Args:
            event_type (EventType): Type of event to subscribe to
            callback (callable): Callback function to call when event is dispatched
        """
        with self._lock:
            if event_type not in self._subscribers:
                self._subscribers[event_type] = []
            self._subscribers[event_type].append(callback)

    def unsubscribe(self, event_type: EventType, callback: Callable[[Event], None]) -> None:
        """
        Unsubscribe from an event type.

        Args:
            event_type (EventType): Type of event to unsubscribe from
            callback (callable): Callback function to remove
        """
        with self._lock:
            if event_type in self._subscribers and callback in self._subscribers[event_type]:
                self._subscribers[event_type].remove(callback)

    def dispatch(self, event: Event) -> None:
        """
        Dispatch an event to all subscribers.

        Args:
            event (Event): Event to dispatch
        """
        with self._lock:
            if event.event_type in self._subscribers:
                # Make a copy of the subscribers list to avoid issues if callbacks modify the list
                subscribers = self._subscribers[event.event_type].copy()
        
        # Call subscribers outside the lock to avoid deadlocks
        for callback in subscribers:
            try:
                callback(event)
            except Exception as e:
                # Log the error but continue with other subscribers
                from searchtools.utils.logging import logger
                logger.error(f"Error in event subscriber: {e}")

# Global event dispatcher instance
event_dispatcher = EventDispatcher()
