2025-06-06 23:38:44,216 - INFO - PyPDF2 configured. PDF parsing support enabled.
2025-06-06 23:38:44,337 - INFO - win32com configured. DOC parsing support enabled.
2025-06-06 23:38:44,341 - DEBUG - antiword not available as fallback for DOC files.
2025-06-06 23:38:44,347 - WARNING - pyexcel-ods not available. ODS support disabled.
2025-06-06 23:38:44,350 - DEBUG - ezodf not available as alternative for ODS files.
2025-06-06 23:38:45,841 - INFO - Application started
2025-06-06 23:38:45,845 - INFO - Custom styling applied
2025-06-06 23:38:45,975 - INFO - rarfile configured. RAR parsing support enabled.
2025-06-06 23:38:47,006 - INFO - py7zr configured. 7z archive parsing support enabled.
2025-06-06 23:38:47,006 - DEBUG - Registered parser: Archive Parser
2025-06-06 23:38:47,009 - DEBUG - Registered parser: CSV Parser
2025-06-06 23:38:47,011 - DEBUG - Registered parser: DOC Parser
2025-06-06 23:38:47,011 - DEBUG - Registered parser: DOCX Parser
2025-06-06 23:38:47,012 - DEBUG - Registered parser: Excel Parser
2025-06-06 23:38:47,012 - DEBUG - Registered parser: ODS Parser
2025-06-06 23:38:47,012 - DEBUG - Registered parser: PDF Parser
2025-06-06 23:38:47,012 - DEBUG - Registered parser: Text Parser
2025-06-06 23:38:47,012 - INFO - Discovered 8 parser plugins
2025-06-06 23:38:47,060 - INFO - Looking for logo at (method 1): C:\Users\<USER>\Desktop\L457\SearchTools-v1\icon.ico
2025-06-06 23:38:47,060 - INFO - Logo file found at: C:\Users\<USER>\Desktop\L457\SearchTools-v1\icon.ico
2025-06-06 23:38:47,263 - INFO - Image opened, mode: RGBA, size: (159, 31)
2025-06-06 23:38:47,266 - INFO - Resized image to 100x19 to maintain aspect ratio
2025-06-06 23:38:47,299 - INFO - Converted to PhotoImage
2025-06-06 23:38:47,305 - INFO - Bottom logo loaded successfully
2025-06-06 23:38:47,535 - INFO - UI initialized
2025-06-06 23:41:02,079 - INFO - Window closed by user
2025-06-06 23:41:02,415 - INFO - Application exited
