"""
Parser for archive files (ZIP, RAR, 7z)
"""

import os
import re
import zipfile
import shutil
from typing import List, Callable

from searchtools.utils.logging import logger
from searchtools.utils.logging_guidelines import (
    log_parser_not_available, log_parser_error,
    log_file_error, log_archive_operation, log_dependency_status
)
from searchtools.utils.file_utils import get_file_extension
from searchtools.utils.error_handling import (
    handle_archive_operation,
    ArchiveDependencyError
)
from searchtools.utils.security import (
    ArchiveSecurityManager, SecurityError, PathTraversalError,
    ResourceExhaustionError
)
from searchtools.utils.temp_manager import temp_manager
from searchtools.core.result import SearchResult
from searchtools.parsers.base_parser import BaseParser

class ArchiveParser(BaseParser):
    """
    Parser for archive files (ZIP, RAR, 7z).

    This class handles the parsing of various archive formats:
    - ZIP archives (always supported)
    - RAR archives (requires rarfile package and unrar executable)
    - 7z archives (requires py7zr package)

    The availability of each format is determined at runtime during initialization.
    """
    # Private class variables to store availability status
    _initialized = False
    _rar_available = False
    _sevenzip_available = False
    _unrar_path = None

    @classmethod
    def _initialize_capabilities(cls) -> None:
        """
        Initialize the parser capabilities by checking for required dependencies.
        This method is called once to determine which archive formats are supported.
        """
        if cls._initialized:
            return

        # ZIP is always available (part of standard library)

        # Check if rarfile is available
        try:
            import rarfile
            # Check for unrar executable: Prefer PATH, then check local dir
            unrar_path = shutil.which('unrar')  # Check PATH first

            if not unrar_path:
                # If not in PATH, check script's directory
                script_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                local_unrar_path_exe = os.path.join(script_dir, 'UnRAR.exe')  # For Windows
                local_unrar_path_noext = os.path.join(script_dir, 'unrar')    # For Linux/Mac

                # Check if the file exists and is executable (simple check)
                if os.path.isfile(local_unrar_path_exe):  # No need for os.X_OK on Windows usually
                    unrar_path = local_unrar_path_exe
                    logger.info(f"Found unrar executable in script directory: {unrar_path}")
                elif os.path.isfile(local_unrar_path_noext) and os.access(local_unrar_path_noext, os.X_OK):
                    unrar_path = local_unrar_path_noext
                    logger.info(f"Found unrar executable in script directory: {unrar_path}")

            if unrar_path:
                # If found in PATH or locally, configure rarfile
                rarfile.custom_tool_path = unrar_path
                rarfile.tool_setup_needed = False  # Suppress interactive prompt
                cls._rar_available = True
                cls._unrar_path = unrar_path
                log_dependency_status("configured", "rarfile", "RAR parsing")
            else:
                log_dependency_status("missing", "unrar executable", "RAR extraction")

        except ImportError:
            log_dependency_status("missing", "rarfile", "RAR parsing")
        except Exception as e:
            log_dependency_status("error", "rarfile", "RAR parsing", e)

        # Check if py7zr is available
        try:
            import py7zr  # noqa: F401 - imported but unused, we're just checking if it's available
            cls._sevenzip_available = True
            log_dependency_status("configured", "py7zr", "7z archive parsing")
        except ImportError:
            log_dependency_status("missing", "py7zr", "7z archive parsing")
        except Exception as e:
            log_dependency_status("error", "py7zr", "7z archive parsing", e)

        cls._initialized = True

    @classmethod
    def get_name(cls) -> str:
        """
        Get the name of the parser.

        Returns:
            str: Parser name
        """
        return "Archive Parser"

    @classmethod
    def get_supported_extensions(cls) -> List[str]:
        """
        Get the list of file extensions supported by this parser.

        Returns:
            List[str]: List of supported file extensions
        """
        # Ensure capabilities are initialized
        cls._initialize_capabilities()

        extensions = ['.zip']
        if cls._rar_available:
            extensions.append('.rar')
        if cls._sevenzip_available:
            extensions.append('.7z')
        return extensions

    @classmethod
    def get_priority(cls) -> int:
        """
        Get the priority of the parser.

        Returns:
            int: Priority value
        """
        return 20  # Higher priority than regular file parsers

    @classmethod
    def is_available(cls) -> bool:
        """
        Check if archive parsing is available.

        Returns:
            bool: True if at least one archive format is supported, False otherwise
        """
        return True  # ZIP is always available

    @classmethod
    def is_rar_available(cls) -> bool:
        """
        Check if RAR parsing is available.

        Returns:
            bool: True if RAR parsing is available, False otherwise
        """
        # Ensure capabilities are initialized
        cls._initialize_capabilities()
        return cls._rar_available

    @classmethod
    def is_7z_available(cls) -> bool:
        """
        Check if 7z parsing is available.

        Returns:
            bool: True if 7z parsing is available, False otherwise
        """
        # Ensure capabilities are initialized
        cls._initialize_capabilities()
        return cls._sevenzip_available

    @classmethod
    def parse(cls, *args, **kwargs) -> List[SearchResult]:  # noqa: F841 - args and kwargs are unused
        """
        Parse an archive file and search for keywords.

        This is a placeholder method to satisfy the BaseParser interface.
        Archive parsing is handled differently through the scanner.

        Returns:
            list: Empty list (archive parsing is handled by the scanner)
        """
        # Archive parsing is handled by the scanner through callbacks
        # This method is just a placeholder to satisfy the interface
        return []

    @classmethod
    def is_archive(cls, file_path: str) -> bool:
        """
        Check if a file is an archive.

        Args:
            file_path (str): Path to the file

        Returns:
            bool: True if the file is an archive, False otherwise
        """
        # Get the file extension and filename
        ext = get_file_extension(file_path)
        file_name = os.path.basename(file_path).lower()

        # Get supported extensions
        supported_extensions = cls.get_supported_extensions()

        # Check if the extension is supported (case-insensitive)
        if ext.lower() in [e.lower() for e in supported_extensions]:
            return True

        # Special case for 7z files without extension or split archives
        if cls._sevenzip_available and '.7z' in supported_extensions:
            # Check for files ending with .7z
            if file_name.endswith('.7z'):
                return True

            # Check for split 7z archives (e.g., .7z.001, .7z.002)
            if re.search(r'\.7z\.\d+$', file_name):
                return True

        return False

    @classmethod
    def parse_zip(cls, file_path: str, file_name: str, scanner_callback: Callable, temp_dirs: List[str]) -> bool:
        """
        Parse a ZIP archive with security validation.

        Args:
            file_path (str): Path to the file
            file_name (str): Name of the file
            scanner_callback (callable): Callback for scanning extracted files
            temp_dirs (list): List to store temporary directories

        Returns:
            bool: True if the archive was processed, False otherwise
        """
        log_archive_operation("started", "ZIP", file_name)

        # Create a secure temp directory using the temp manager
        temp_dir = temp_manager.create_temp_dir(prefix=f"zip_{os.path.basename(file_name)[:20]}_")
        temp_dirs.append(temp_dir)  # Also add to legacy list for compatibility

        def extract_and_scan():
            # Initialize security manager
            security_manager = ArchiveSecurityManager()
            security_manager.reset_counters()

            with zipfile.ZipFile(file_path, 'r') as zip_ref:
                # Get list of files before extraction for progress tracking
                file_list = zip_ref.namelist()
                total_files = len(file_list)
                logger.debug(f"ZIP contains {total_files} files")

                # Validate and extract files securely
                for file_info in file_list:
                    # Skip directories
                    if file_info.endswith('/'):
                        continue

                    try:
                        # Get file info for size validation
                        zip_info = zip_ref.getinfo(file_info)
                        file_size = zip_info.file_size

                        # Validate extraction security
                        safe_path = security_manager.validate_extraction(
                            file_info, file_size, temp_dir
                        )

                        # Extract the file securely
                        zip_ref.extract(file_info, temp_dir)
                        logger.debug(f"Safely extracted: {safe_path}")

                    except (SecurityError, PathTraversalError, ResourceExhaustionError) as ex:
                        logger.warning(f"Security violation extracting {file_info}: {ex}")
                        # Continue with other files but log the security issue
                        continue
                    except Exception as ex:
                        logger.error(f"Error extracting {file_info}: {ex}")
                        continue

                # Log extraction statistics
                stats = security_manager.get_extraction_stats()
                logger.info(f"ZIP extraction completed: {stats['files_extracted']} files, "
                           f"{stats['total_size']} bytes")

                log_archive_operation("completed", "ZIP", file_name, temp_dir)
                # Recursively scan the extracted directory
                scanner_callback(temp_dir)
                return True

        # Use the standardized error handling utility
        result = handle_archive_operation(
            extract_and_scan,
            "ZIP",
            file_name,
            "Failed to extract ZIP archive"
        )

        return result is not None

    @classmethod
    def parse_rar(cls, file_path: str, file_name: str, scanner_callback: Callable, temp_dirs: List[str]) -> bool:
        """
        Parse a RAR archive with security validation.

        Args:
            file_path (str): Path to the file
            file_name (str): Name of the file
            scanner_callback (callable): Callback for scanning extracted files
            temp_dirs (list): List to store temporary directories

        Returns:
            bool: True if the archive was processed, False otherwise
        """
        # Ensure capabilities are initialized
        cls._initialize_capabilities()

        if not cls._rar_available:
            log_parser_not_available("RAR", file_name)
            return False

        log_archive_operation("started", "RAR", file_name)

        # Create a secure temp directory using the temp manager
        temp_dir = temp_manager.create_temp_dir(prefix=f"rar_{os.path.basename(file_name)[:20]}_")
        temp_dirs.append(temp_dir)  # Also add to legacy list for compatibility

        def extract_and_scan():
            # Import rarfile here to avoid module-level import issues
            import rarfile

            # Initialize security manager
            security_manager = ArchiveSecurityManager()
            security_manager.reset_counters()

            with rarfile.RarFile(file_path, 'r') as rar_ref:
                # Get list of files before extraction for progress tracking
                file_list = rar_ref.namelist()
                total_files = len(file_list)
                logger.info(f"RAR contains {total_files} files")

                # Validate and extract files securely
                for file_info in file_list:
                    # Skip directories
                    if file_info.endswith('/'):
                        continue

                    try:
                        # Get file info for size validation
                        rar_info = rar_ref.getinfo(file_info)
                        file_size = rar_info.file_size

                        # Validate extraction security
                        safe_path = security_manager.validate_extraction(
                            file_info, file_size, temp_dir
                        )

                        # Extract the file securely
                        rar_ref.extract(file_info, temp_dir)
                        logger.debug(f"Safely extracted: {safe_path}")

                    except (SecurityError, PathTraversalError, ResourceExhaustionError) as ex:
                        logger.warning(f"Security violation extracting {file_info}: {ex}")
                        # Continue with other files but log the security issue
                        continue
                    except Exception as ex:
                        logger.error(f"Error extracting {file_info} from RAR: {ex}")
                        continue

                # Log extraction statistics
                stats = security_manager.get_extraction_stats()
                logger.info(f"RAR extraction completed: {stats['files_extracted']} files, "
                           f"{stats['total_size']} bytes")

                log_archive_operation("completed", "RAR", file_name, temp_dir)
                scanner_callback(temp_dir)
                return True

        # Use the standardized error handling utility
        try:
            result = handle_archive_operation(
                extract_and_scan,
                "RAR",
                file_name,
                "Failed to extract RAR archive"
            )
            return result is not None
        except ArchiveDependencyError:
            # This should never happen since we check availability first,
            # but handle it just in case
            log_parser_error("RAR", file_name, Exception("Dependency error despite availability check"))
            return False

    @classmethod
    def parse_7z(cls, file_path: str, file_name: str, scanner_callback: Callable, temp_dirs: List[str]) -> bool:
        """
        Parse a 7z archive.

        Args:
            file_path (str): Path to the file
            file_name (str): Name of the file
            scanner_callback (callable): Callback for scanning extracted files
            temp_dirs (list): List to store temporary directories

        Returns:
            bool: True if the archive was processed, False otherwise
        """
        # Ensure capabilities are initialized
        cls._initialize_capabilities()

        if not cls._sevenzip_available:
            log_parser_not_available("7z", file_name)
            return False

        log_archive_operation("started", "7z", file_name)
        logger.debug(f"Full path: {file_path}")

        # Verify file exists and is accessible
        if not os.path.isfile(file_path):
            log_file_error("not_found", file_path)
            return False

        # Log file size for debugging
        file_size = os.path.getsize(file_path)
        logger.debug(f"7z file size: {file_size/1024/1024:.2f} MB")

        # Create a secure temp directory using the temp manager
        temp_dir = temp_manager.create_temp_dir(prefix=f"7z_{os.path.basename(file_name)[:20]}_")
        temp_dirs.append(temp_dir)  # Also add to legacy list for compatibility
        logger.debug(f"Created secure temp directory: {temp_dir}")

        def extract_and_scan():
            # Import py7zr here to avoid module-level import issues
            import py7zr

            # Initialize security manager
            security_manager = ArchiveSecurityManager()
            security_manager.reset_counters()

            # Open the 7z file
            logger.debug("Opening 7z file...")
            with py7zr.SevenZipFile(file_path, mode='r') as seven_ref:
                # Get list of files before extraction for progress tracking
                file_list = seven_ref.getnames()
                total_files = len(file_list)
                logger.debug(f"7z archive contains {total_files} files/folders: {', '.join(file_list[:10])}{'...' if len(file_list) > 10 else ''}")

                # Extract files securely one by one
                logger.debug("Starting secure extraction of 7z archive...")

                for file_info in file_list:
                    # Skip directories
                    if file_info.endswith('/'):
                        continue

                    try:
                        # Note: py7zr doesn't provide easy access to individual file sizes
                        # We'll validate the size after extraction

                        # Validate path security first
                        safe_path = security_manager.path_validator.validate_path(file_info, temp_dir)

                        # Check file count limit
                        if security_manager.files_extracted >= security_manager.max_files_per_archive:
                            raise ResourceExhaustionError(
                                f"Too many files in archive (limit: {security_manager.max_files_per_archive})"
                            )

                        # Extract individual file
                        seven_ref.extract(path=temp_dir, targets=[file_info])

                        # Check actual extracted file size
                        extracted_path = os.path.join(temp_dir, file_info)
                        if os.path.exists(extracted_path):
                            actual_size = os.path.getsize(extracted_path)

                            # Validate size limits
                            if actual_size > security_manager.max_individual_file_size:
                                os.remove(extracted_path)  # Remove the oversized file
                                raise ResourceExhaustionError(
                                    f"File too large: {file_info} ({actual_size} bytes)"
                                )

                            # Update counters
                            security_manager.total_extracted_size += actual_size
                            security_manager.files_extracted += 1

                            # Check total size limit
                            if security_manager.total_extracted_size > security_manager.max_extracted_size:
                                os.remove(extracted_path)  # Remove the file that exceeded limit
                                raise ResourceExhaustionError(
                                    f"Archive too large when extracted (limit: {security_manager.max_extracted_size} bytes)"
                                )

                        logger.debug(f"Safely extracted: {safe_path}")

                    except (SecurityError, PathTraversalError, ResourceExhaustionError) as ex:
                        logger.warning(f"Security violation extracting {file_info}: {ex}")
                        # Continue with other files but log the security issue
                        continue
                    except Exception as ex:
                        logger.error(f"Error extracting {file_info} from 7z: {ex}")
                        continue

                # Log extraction statistics
                stats = security_manager.get_extraction_stats()
                logger.info(f"7z extraction completed: {stats['files_extracted']} files, "
                           f"{stats['total_size']} bytes")

                logger.debug(f"Extraction completed to: {temp_dir}")

                log_archive_operation("completed", "7z", file_name, temp_dir)
                # Scan the extracted directory
                scanner_callback(temp_dir)
                return True

        # Use the standardized error handling utility
        try:
            result = handle_archive_operation(
                extract_and_scan,
                "7z",
                file_name,
                "Failed to extract 7z archive"
            )
            return result is not None
        except ArchiveDependencyError:
            # This should never happen since we check availability first,
            # but handle it just in case
            log_parser_error("7z", file_name, Exception("Dependency error despite availability check"))
            return False
