"""
Unit tests for parsers
"""

import os
import unittest
import tempfile
from typing import List

from searchtools.parsers.plugin_manager import plugin_manager
from searchtools.parsers.base_parser import BaseParser
from searchtools.core.result import SearchResult

class TestParsers(unittest.TestCase):
    """
    Test case for parsers.
    """
    
    def setUp(self):
        """
        Set up the test case.
        """
        # Initialize plugin manager
        plugin_manager.discover_parsers()
        
        # Create a temporary directory for test files
        self.temp_dir = tempfile.mkdtemp()
        
        # Create a test text file
        self.text_file_path = os.path.join(self.temp_dir, "test.txt")
        with open(self.text_file_path, "w") as f:
            f.write("This is a test file.\n")
            f.write("It contains some test keywords.\n")
            f.write("The keywords are: test, keyword, file.\n")
    
    def tearDown(self):
        """
        Clean up after the test case.
        """
        # Remove the temporary directory
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_plugin_manager(self):
        """
        Test the plugin manager.
        """
        # Check that parsers were discovered
        parsers = plugin_manager.get_all_parsers()
        self.assertGreater(len(parsers), 0, "No parsers were discovered")
        
        # Check that the text parser is available
        text_parser = None
        for parser in parsers:
            if parser.get_name() == "Text Parser":
                text_parser = parser
                break
        
        self.assertIsNotNone(text_parser, "Text parser not found")
        self.assertTrue(text_parser.is_available(), "Text parser is not available")
    
    def test_text_parser(self):
        """
        Test the text parser.
        """
        # Get the text parser
        parser_class = plugin_manager.get_parser_for_file(self.text_file_path)
        self.assertIsNotNone(parser_class, "No parser found for text file")
        
        # Parse the file
        results = parser_class.parse(
            self.text_file_path,
            os.path.basename(self.text_file_path),
            ["test", "keyword"],
            False
        )
        
        # Check the results
        self.assertIsInstance(results, list, "Parser did not return a list")
        self.assertGreater(len(results), 0, "Parser did not find any matches")
        
        # Check that all results are SearchResult objects
        for result in results:
            self.assertIsInstance(result, SearchResult, "Result is not a SearchResult object")
            self.assertIn(result.keyword, ["test", "keyword"], "Result keyword is not one of the search keywords")
    
    def test_parser_interface(self):
        """
        Test that all parsers implement the required interface.
        """
        parsers = plugin_manager.get_all_parsers()
        
        for parser in parsers:
            # Check that the parser is a subclass of BaseParser
            self.assertTrue(issubclass(parser, BaseParser), f"{parser.__name__} is not a subclass of BaseParser")
            
            # Check that the parser implements the required methods
            self.assertTrue(hasattr(parser, "get_name"), f"{parser.__name__} does not implement get_name")
            self.assertTrue(hasattr(parser, "get_supported_extensions"), f"{parser.__name__} does not implement get_supported_extensions")
            self.assertTrue(hasattr(parser, "is_available"), f"{parser.__name__} does not implement is_available")
            self.assertTrue(hasattr(parser, "parse"), f"{parser.__name__} does not implement parse")
            
            # Check method return types
            self.assertIsInstance(parser.get_name(), str, f"{parser.__name__}.get_name() does not return a string")
            self.assertIsInstance(parser.get_supported_extensions(), list, f"{parser.__name__}.get_supported_extensions() does not return a list")
            self.assertIsInstance(parser.is_available(), bool, f"{parser.__name__}.is_available() does not return a boolean")

if __name__ == "__main__":
    unittest.main()
