"""
Parser for DOC files (older Microsoft Word format)
"""

from typing import List
import os
import subprocess

from searchtools.utils.logging import logger
import config
from searchtools.utils.logging_guidelines import (
    log_parser_not_available, log_parser_error, log_parser_fallback,
    log_file_error, log_search_match, log_dependency_status
)
from searchtools.core.searcher import TextSearcher
from searchtools.core.result import SearchResult
from searchtools.parsers.base_parser import BaseParser

# Check if win32com is available (for Windows systems)
WIN32COM_AVAILABLE = False
try:
    import win32com.client
    WIN32COM_AVAILABLE = True
    log_dependency_status("configured", "win32com", "DOC parsing")
except ImportError:
    log_dependency_status("missing", "win32com", "DOC support")

# Check if antiword is available as a fallback
ANTIWORD_AVAILABLE = False
try:
    # Try to run antiword to check if it's installed
    subprocess.run(["antiword", "-h"], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    ANTIWORD_AVAILABLE = True
    log_dependency_status("configured", "antiword", "DOC fallback parsing")
except (subprocess.SubprocessError, FileNotFoundError):
    logger.debug("antiword not available as fallback for DOC files.")

class DocParser(BaseParser):
    """
    Parser for DOC files (older Microsoft Word format).
    """

    @classmethod
    def get_name(cls) -> str:
        """
        Get the name of the parser.

        Returns:
            str: Parser name
        """
        return "DOC Parser"

    @classmethod
    def get_supported_extensions(cls) -> List[str]:
        """
        Get the list of file extensions supported by this parser.

        Returns:
            List[str]: List of supported file extensions
        """
        return ['.doc']

    @classmethod
    def get_priority(cls) -> int:
        """
        Get the priority of the parser.

        Returns:
            int: Priority value
        """
        return 10

    @classmethod
    def is_available(cls) -> bool:
        """
        Check if DOC parsing is available.

        Returns:
            bool: True if DOC parsing is available, False otherwise
        """
        return WIN32COM_AVAILABLE or ANTIWORD_AVAILABLE

    @classmethod
    def parse(cls, file_path: str, file_name: str, keywords: List[str], case_sensitive: bool = False,
             use_regex: bool = False, whole_word: bool = False) -> List[SearchResult]:
        """
        Parse a DOC file and search for keywords.

        Args:
            file_path (str): Path to the file
            file_name (str): Name of the file
            keywords (list): List of keywords to search for
            case_sensitive (bool, optional): Whether to use case-sensitive search. Defaults to False.
            use_regex (bool, optional): Whether to treat keywords as regular expressions. Defaults to False.
            whole_word (bool, optional): Whether to match whole words only. Defaults to False.

        Returns:
            list: List of SearchResult objects
        """
        if not cls.is_available():
            log_parser_not_available("DOC", file_name)
            return []

        results = []
        text = None

        # Try win32com first if available (Windows only)
        if WIN32COM_AVAILABLE:
            word = None
            try:
                # Get absolute path
                abs_path = os.path.abspath(file_path)

                # Check if file exists and is readable
                if not os.path.isfile(abs_path):
                    log_file_error("not_found", abs_path)
                    raise FileNotFoundError(f"File not found: {abs_path}")

                # Check file size - if too small, likely not a valid DOC file
                file_size = os.path.getsize(abs_path)
                if file_size < config.MIN_DOC_FILE_SIZE:
                    logger.warning(f"DOC file too small ({file_size} bytes), likely not a valid DOC file: {file_name}")
                    raise ValueError(f"File too small to be a valid DOC file: {file_name}")

                # Check if the file is actually a DOC file by checking its header
                try:
                    with open(file_path, 'rb') as f:
                        header = f.read(8)
                        # Check for DOC file signature (D0CF11E0)
                        if not header.startswith(b'\xD0\xCF\x11\xE0'):
                            logger.warning(f"File does not have DOC signature, might not be a DOC file: {file_name}")
                            # Continue anyway, as some DOC files might have different headers
                except Exception as e:
                    logger.warning(f"Could not check DOC file header: {file_name}, {e}")

                # Initialize Word application with error handling
                try:
                    # Use a timeout mechanism to prevent hanging
                    import threading
                    import time

                    word_initialized = False
                    word_error = None

                    def init_word():
                        nonlocal word, word_initialized, word_error
                        try:
                            word = win32com.client.Dispatch("Word.Application")
                            word_initialized = True
                        except Exception as e:
                            word_error = e

                    # Start Word initialization in a separate thread
                    word_thread = threading.Thread(target=init_word)
                    word_thread.daemon = True
                    word_thread.start()

                    # Wait for Word to initialize with a timeout
                    timeout = config.WORD_INIT_TIMEOUT
                    start_time = time.time()
                    while not word_initialized and time.time() - start_time < timeout:
                        time.sleep(config.TIMEOUT_SLEEP_INTERVAL)

                    if not word_initialized:
                        if word_error:
                            raise word_error
                        else:
                            raise TimeoutError("Word initialization timed out")
                except Exception as e:
                    logger.warning(f"Failed to initialize Word application for {file_name}: {e}")
                    raise

                # Set visibility with error handling - continue even if this fails
                try:
                    word.Visible = False
                except Exception as e:
                    logger.warning(f"Failed to set Word.Application.Visible for {file_name}: {e}")
                    # Continue anyway, this is not critical

                # Open the document with error handling and timeout
                doc = None
                try:
                    doc_opened = False
                    doc_error = None

                    def open_doc():
                        nonlocal doc, doc_opened, doc_error
                        try:
                            doc = word.Documents.Open(abs_path)
                            doc_opened = True
                        except Exception as e:
                            doc_error = e

                    # Start document opening in a separate thread
                    doc_thread = threading.Thread(target=open_doc)
                    doc_thread.daemon = True
                    doc_thread.start()

                    # Wait for document to open with a timeout
                    timeout = config.DOC_OPEN_TIMEOUT
                    start_time = time.time()
                    while not doc_opened and time.time() - start_time < timeout:
                        time.sleep(config.TIMEOUT_SLEEP_INTERVAL)

                    if not doc_opened:
                        if doc_error:
                            raise doc_error
                        else:
                            raise TimeoutError("Document opening timed out")
                except Exception as e:
                    logger.warning(f"Failed to open document {file_name}: {e}")
                    if word:
                        try:
                            word.Quit()
                        except:
                            pass
                    raise

                # Extract text with error handling and timeout
                try:
                    text_extracted = False
                    text_error = None
                    text = None

                    def extract_text():
                        nonlocal text, text_extracted, text_error
                        try:
                            text = doc.Content.Text
                            text_extracted = True
                        except Exception as e:
                            text_error = e

                    # Start text extraction in a separate thread
                    text_thread = threading.Thread(target=extract_text)
                    text_thread.daemon = True
                    text_thread.start()

                    # Wait for text extraction with a timeout
                    timeout = config.TEXT_EXTRACT_TIMEOUT
                    start_time = time.time()
                    while not text_extracted and time.time() - start_time < timeout:
                        time.sleep(config.TIMEOUT_SLEEP_INTERVAL)

                    if not text_extracted:
                        if text_error:
                            raise text_error
                        else:
                            raise TimeoutError("Text extraction timed out")
                except Exception as e:
                    logger.warning(f"Failed to extract text from {file_name}: {e}")
                    try:
                        if doc:
                            doc.Close(False)
                        if word:
                            word.Quit()
                    except Exception as cleanup_err:
                        logger.debug(f"Error during cleanup after text extraction failure: {cleanup_err}")
                    raise

                # Close the document and quit Word with error handling
                try:
                    if doc:
                        doc.Close(False)
                except Exception as e:
                    logger.warning(f"Failed to close document {file_name}: {e}")

                try:
                    if word:
                        word.Quit()
                except Exception as e:
                    logger.warning(f"Failed to quit Word application for {file_name}: {e}")

                logger.debug(f"Extracted text from DOC using win32com: {file_name}")
            except Exception as e:
                log_parser_error("win32com", file_name, e)
                # Make sure Word is closed if an error occurred
                if word:
                    try:
                        word.Quit()
                    except Exception as quit_err:
                        logger.debug(f"Error quitting Word application: {quit_err}")
                text = None

        # Fall back to antiword if win32com failed
        if text is None and ANTIWORD_AVAILABLE:
            log_parser_fallback("antiword", file_name)
            try:
                result = subprocess.run(
                    ["antiword", file_path],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                if result.returncode == 0:
                    text = result.stdout
                    logger.debug(f"Extracted text from DOC using antiword: {file_name}")
                else:
                    log_parser_error("antiword", file_name, Exception(result.stderr))
            except Exception as e:
                log_parser_error("antiword", file_name, e)

        # Last resort: try to read as binary and extract text
        if text is None:
            log_parser_fallback("binary", file_name)
            try:
                # Try to read as binary and extract any readable text
                with open(file_path, 'rb') as f:
                    binary_data = f.read()

                # Try to decode as UTF-8 with error handling
                try:
                    text = binary_data.decode('utf-8', errors='ignore')
                    logger.debug(f"Extracted text from DOC using binary fallback (UTF-8): {file_name}")
                except Exception:
                    # Try other common encodings
                    for encoding in ['latin-1', 'cp1252', 'ascii']:
                        try:
                            text = binary_data.decode(encoding, errors='ignore')
                            logger.debug(f"Extracted text from DOC using binary fallback ({encoding}): {file_name}")
                            break
                        except Exception:
                            continue

                # Clean up the text - remove non-printable characters
                if text:
                    import string
                    printable = set(string.printable)
                    text = ''.join(c for c in text if c in printable)

                    # If the text is mostly garbage (high ratio of special characters), discard it
                    if len(text.strip()) < 10 or sum(c.isalnum() for c in text) / len(text) < 0.3:
                        logger.warning(f"Extracted text appears to be garbage, discarding: {file_name}")
                        text = None
            except Exception as e:
                log_parser_error("binary fallback", file_name, e)
                text = None

        # If we have text, search for keywords
        if text:
            # Split text into paragraphs
            paragraphs = text.split('\n\n')
            for para_idx, para in enumerate(paragraphs):
                if para.strip():
                    found_keyword = TextSearcher.search_text(para, keywords, case_sensitive, use_regex, whole_word)
                    if found_keyword:
                        location = f"Para: {para_idx+1}"
                        log_search_match("DOC", file_name, location, found_keyword)
                        results.append(
                            SearchResult(
                                file=file_name,
                                path=file_path,
                                keyword=found_keyword,
                                context=para.strip(),
                                location=f"DOC: Paragraph {para_idx+1}",
                                full_context=para.strip()
                            )
                        )
        else:
            logger.warning(f"Could not extract text from DOC file: {file_name}")

        return results
