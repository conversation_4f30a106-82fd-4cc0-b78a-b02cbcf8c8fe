"""
Scan Controller for SearchTools application
Handles scan operations and scanner interaction
"""

import os
import threading
from tkinter import messagebox

from searchtools.core.scanner import Scanner
from searchtools.utils.logging import logger
from searchtools.utils.error_handling import handle_ui_operation
import config


class Scan<PERSON><PERSON>roller:
    """
    Controls scan operations and manages scanner interaction.
    """
    
    def __init__(self):
        """Initialize the scan controller."""
        self.scanner = Scanner()
        self.scan_in_progress = False
        
        # Callbacks for UI updates
        self.progress_callback = None
        self.status_callback = None
        self.result_callback = None
        self.file_progress_callback = None
        self.scan_state_callback = None
        
        # Set up scanner callbacks
        self._setup_scanner_callbacks()
    
    def _setup_scanner_callbacks(self):
        """Set up callbacks for the scanner."""
        self.scanner.set_callbacks(
            progress_callback=self._on_progress_update,
            status_callback=self._on_status_update,
            result_callback=self._on_result_found,
            file_progress_callback=self._on_file_progress_update
        )
    
    def set_callbacks(self, progress_callback=None, status_callback=None, 
                     result_callback=None, file_progress_callback=None,
                     scan_state_callback=None):
        """
        Set callbacks for UI updates.
        
        Args:
            progress_callback: Callback for overall progress updates
            status_callback: Callback for status updates
            result_callback: Callback for result updates
            file_progress_callback: Callback for file-level progress updates
            scan_state_callback: Callback for scan state changes (start/stop)
        """
        self.progress_callback = progress_callback
        self.status_callback = status_callback
        self.result_callback = result_callback
        self.file_progress_callback = file_progress_callback
        self.scan_state_callback = scan_state_callback
    
    @handle_ui_operation(error_message="Error starting scan")
    def start_scan(self, folder, keywords_text, case_sensitive=False, 
                   use_regex=False, whole_word=False):
        """
        Start a scan operation.
        
        Args:
            folder (str): Folder or file path to scan
            keywords_text (str): Comma-separated keywords
            case_sensitive (bool): Whether to use case-sensitive search
            use_regex (bool): Whether to treat keywords as regular expressions
            whole_word (bool): Whether to match whole words only
            
        Returns:
            bool: True if scan started successfully, False otherwise
        """
        # Validate inputs
        if not self._validate_scan_inputs(folder, keywords_text):
            return False
        
        # Parse keywords
        keywords = self._parse_keywords(keywords_text)
        if not keywords:
            messagebox.showerror("Error", "Please enter at least one keyword")
            return False
        
        # Update scan state
        self.scan_in_progress = True
        if self.scan_state_callback:
            self.scan_state_callback(True)
        
        # Log scan start
        logger.info(f"Starting scan of {folder}")
        logger.info(f"Keywords: {', '.join(keywords)}")
        logger.info(f"Options: case_sensitive={case_sensitive}, use_regex={use_regex}, whole_word={whole_word}")
        
        # Start the scan
        self.scanner.start_scan(folder, keywords, case_sensitive, use_regex, whole_word)
        
        return True
    
    def stop_scan(self):
        """Stop the current scan operation."""
        if self.scan_in_progress:
            logger.info("Stop scan requested by user")
            self.scanner.stop_scanning()
            
            # Start monitoring the stop progress
            self._monitor_stop_progress()
    
    def _validate_scan_inputs(self, folder, keywords_text):
        """
        Validate scan inputs.
        
        Args:
            folder (str): Folder or file path to scan
            keywords_text (str): Keywords text
            
        Returns:
            bool: True if inputs are valid, False otherwise
        """
        # Import ArchiveParser to check if the path is an archive
        from searchtools.parsers.archive_parser import ArchiveParser
        
        # Validate folder/file path
        if not folder:
            messagebox.showerror("Error", "Please select a valid folder or file")
            return False
        
        # Check if it's a directory or a valid archive file
        is_valid_dir = os.path.isdir(folder)
        is_valid_archive = os.path.isfile(folder) and ArchiveParser.is_archive(folder)
        
        if not is_valid_dir and not is_valid_archive:
            messagebox.showerror("Error", "Please select a valid folder or archive file (ZIP, RAR, 7Z)")
            return False
        
        # Validate keywords
        if not keywords_text.strip():
            messagebox.showerror("Error", "Please enter at least one keyword")
            return False
        
        return True
    
    def _parse_keywords(self, keywords_text):
        """
        Parse keywords from text input.
        
        Args:
            keywords_text (str): Comma-separated keywords
            
        Returns:
            list: List of parsed keywords
        """
        # Split by comma and clean up
        keywords = [keyword.strip() for keyword in keywords_text.split(",")]
        keywords = [keyword for keyword in keywords if keyword]  # Remove empty strings
        
        return keywords
    
    def _monitor_stop_progress(self):
        """Monitor the stopping progress and update the UI."""
        def monitor():
            # Wait a bit for the scanner to start stopping
            import time
            time.sleep(0.5)
            
            # Check if scan is still in progress
            if self.scanner.scan_in_progress:
                if self.status_callback:
                    self.status_callback("Stopping scan...")
                
                # Start a timer to check periodically
                self._check_stop_progress()
        
        # Run monitoring in a separate thread
        monitor_thread = threading.Thread(target=monitor, daemon=True)
        monitor_thread.start()
    
    def _check_stop_progress(self):
        """Check if the scan has stopped and update UI accordingly."""
        if not self.scanner.scan_in_progress:
            # Scan has stopped
            self.scan_in_progress = False
            if self.scan_state_callback:
                self.scan_state_callback(False)
            
            if self.status_callback:
                self.status_callback("Scan stopped")
        else:
            # Still stopping, check again in a bit
            import time
            time.sleep(0.5)
            self._check_stop_progress()
    
    def cleanup_temp_dirs(self):
        """Clean up temporary directories."""
        self.scanner.cleanup_temp_dirs()
    
    def get_results(self):
        """
        Get current scan results.
        
        Returns:
            list: List of SearchResult objects
        """
        return self.scanner.results
    
    def is_scan_in_progress(self):
        """
        Check if a scan is currently in progress.
        
        Returns:
            bool: True if scan is in progress, False otherwise
        """
        return self.scan_in_progress and self.scanner.scan_in_progress
    
    # Scanner callback handlers
    def _on_progress_update(self, progress):
        """Handle progress updates from scanner."""
        if self.progress_callback:
            self.progress_callback(progress)
    
    def _on_status_update(self, status):
        """Handle status updates from scanner."""
        if self.status_callback:
            self.status_callback(status)
    
    def _on_result_found(self, result):
        """Handle new results from scanner."""
        if self.result_callback:
            self.result_callback(result)
    
    def _on_file_progress_update(self, progress, file_name):
        """Handle file-level progress updates from scanner."""
        if self.file_progress_callback:
            self.file_progress_callback(progress, file_name)
    
    def get_scanner_keywords(self):
        """
        Get the current keywords from the scanner.
        
        Returns:
            list: Current keywords being searched
        """
        return self.scanner.keywords if hasattr(self.scanner, 'keywords') else []


class ScanStateManager:
    """
    Manages scan state and UI state transitions.
    """
    
    def __init__(self, input_manager, status_manager):
        """
        Initialize the scan state manager.
        
        Args:
            input_manager: InputFrameManager instance
            status_manager: StatusFrameManager instance
        """
        self.input_manager = input_manager
        self.status_manager = status_manager
        self.scan_in_progress = False
    
    def on_scan_started(self):
        """Handle scan start state changes."""
        self.scan_in_progress = True
        
        # Update button states
        self.input_manager.set_button_states(
            start_enabled=False,
            stop_enabled=True,
            export_enabled=False,
            clear_enabled=False
        )
        
        # Reset progress bars
        self.status_manager.reset_progress_bars()
        
        # Update status
        self.status_manager.update_status("Initializing scan...")
    
    def on_scan_stopped(self):
        """Handle scan stop state changes."""
        self.scan_in_progress = False
        
        # Update button states
        self.input_manager.set_button_states(
            start_enabled=True,
            stop_enabled=False,
            export_enabled=True,  # Enable if there are results
            clear_enabled=True    # Enable if there are results
        )
        
        # Reset progress bar style
        self.status_manager.set_progress_bar_style("TProgressbar")
    
    def on_scan_stopping(self):
        """Handle scan stopping state changes."""
        # Change progress bar style to warning
        self.status_manager.set_progress_bar_style("Warning.TProgressbar")
        
        # Update status
        self.status_manager.update_status("Stopping scan...")
    
    def update_button_states_for_results(self, has_results):
        """
        Update button states based on whether there are results.
        
        Args:
            has_results (bool): Whether there are search results
        """
        if not self.scan_in_progress:
            self.input_manager.set_button_states(
                start_enabled=True,
                stop_enabled=False,
                export_enabled=has_results,
                clear_enabled=has_results
            )
