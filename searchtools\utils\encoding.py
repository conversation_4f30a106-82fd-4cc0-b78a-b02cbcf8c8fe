"""
Encoding detection and handling utilities
"""

import os
from typing import List, <PERSON><PERSON>

import chardet
from searchtools.utils.logging import logger
from searchtools.utils.error_handling import safe_operation, EncodingError

def detect_encoding(file_path: str) -> str:
    """
    Detect file encoding using chardet with optimized reading.

    Args:
        file_path (str): Path to the file

    Returns:
        str: Detected encoding or 'utf-8' as fallback
    """
    def _detect():
        # Only sample first part of file for speed
        sample_size = min(10000, os.path.getsize(file_path))
        with open(file_path, 'rb') as f:
            raw_data = f.read(sample_size)
            if not raw_data:
                logger.warning(f"Empty file: {os.path.basename(file_path)}")
                return 'utf-8'

            result = chardet.detect(raw_data)
            encoding = result['encoding']
            confidence = result['confidence']

            # Use utf-8 as default for common cases
            if encoding is None or confidence < 0.7:
                logger.debug(f"Low confidence encoding ({confidence:.2f}) for {os.path.basename(file_path)}, using utf-8")
                return 'utf-8'
            if encoding and encoding.lower() in ('ascii', 'utf-8', 'utf8'):
                return 'utf-8'

            logger.debug(f"Detected encoding: {encoding} with confidence {confidence:.2f} for {os.path.basename(file_path)}")
            return encoding or 'utf-8'

    # Use safe_operation to handle exceptions
    from searchtools.utils.error_handling import ErrorHandlingPolicy, ErrorAction, LogLevel

    # Create a policy for encoding detection
    policy = ErrorHandlingPolicy(
        action=ErrorAction.RETURN_DEFAULT,
        log_level=LogLevel.ERROR,
        default_value='utf-8',
        include_traceback=True
    )

    return safe_operation(
        _detect,
        f"Encoding detection error for {os.path.basename(file_path)}",
        policy=policy
    )

def read_binary_fallback(file_path: str) -> List[Tuple[int, str]]:
    """
    Read file in binary mode and try different encodings.

    Args:
        file_path (str): Path to the file

    Returns:
        list: List of tuples (line_number, line_text)

    Raises:
        EncodingError: If the file cannot be decoded with any of the attempted encodings
    """
    # Read the entire file
    try:
        with open(file_path, 'rb') as f:
            raw_bytes = f.read()
    except (FileNotFoundError, PermissionError) as e:
        logger.error(f"Cannot access file {os.path.basename(file_path)}: {e}")
        raise EncodingError(f"Cannot access file {os.path.basename(file_path)}: {e}") from e
    except Exception as e:
        logger.error(f"Error reading file {os.path.basename(file_path)}: {e}")
        raise EncodingError(f"Error reading file {os.path.basename(file_path)}: {e}") from e

    # Try different encodings
    encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1', 'ascii', 'utf-16', 'utf-32']
    lines = []
    success = False

    for enc in encodings:
        try:
            text = raw_bytes.decode(enc, errors='replace')
            # Split into lines
            for i, line in enumerate(text.splitlines()):
                lines.append((i + 1, line))
            logger.info(f"Successfully decoded {os.path.basename(file_path)} with {enc}")
            success = True
            break
        except Exception:
            continue

    if not success and not lines:
        logger.error(f"Failed to decode {os.path.basename(file_path)} with any encoding")
        # Return empty list instead of raising an exception to allow partial results
        # This is consistent with the previous behavior

    return lines
