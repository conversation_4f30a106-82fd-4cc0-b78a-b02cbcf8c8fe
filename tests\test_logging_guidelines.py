"""
Test script for logging guidelines implementation
"""

import sys
import os
from searchtools.utils.logging_guidelines import (
    log_parser_not_available, log_parser_error, log_parser_fallback,
    log_file_error, log_archive_operation, log_dependency_status,
    log_search_match, log_search_operation
)

def test_logging_guidelines():
    """Test the logging guidelines implementation"""
    print("Testing logging guidelines implementation...")
    
    # Test parser logging
    print("\nTesting parser logging...")
    log_parser_not_available("PDF", "test.pdf")
    log_parser_error("Excel", "test.xlsx", Exception("Test error"))
    log_parser_fallback("Text Parser", "test.doc")
    
    # Test file error logging
    print("\nTesting file error logging...")
    log_file_error("not_found", "test.txt")
    log_file_error("access_error", "test.txt")
    log_file_error("read_error", "test.txt", Exception("Test error"))
    log_file_error("write_error", "test.txt")
    
    # Test archive operation logging
    print("\nTesting archive operation logging...")
    log_archive_operation("started", "ZIP", "test.zip")
    log_archive_operation("completed", "ZIP", "test.zip", temp_dir="C:/temp")
    log_archive_operation("failed", "RAR", "test.rar", error=Exception("Test error"))
    
    # Test dependency status logging
    print("\nTesting dependency status logging...")
    log_dependency_status("missing", "PyPDF2", "PDF parsing")
    log_dependency_status("error", "rarfile", "RAR parsing", error=Exception("Test error"))
    log_dependency_status("configured", "py7zr", "7z parsing")
    
    # Test search match logging
    print("\nTesting search match logging...")
    log_search_match("PDF", "test.pdf", "Page: 1", "test")
    log_search_match("Excel", "test.xlsx", "Cell: A1", "test")
    
    # Test search operation logging
    print("\nTesting search operation logging...")
    log_search_operation("started", folder_path="C:/test")
    log_search_operation("completed", item_count=10, item_type="files", file_name="test.zip")
    log_search_operation("finished", status="complete", match_count=5)
    
    print("\nAll tests completed.")

if __name__ == "__main__":
    test_logging_guidelines()
