"""
Result Manager for SearchTools application
Handles result display, management, and export functionality
"""

import os
import csv
import queue
import tkinter as tk
from tkinter import ttk, filedialog, messagebox

from searchtools.core.result import SearchResult
from searchtools.utils.logging import logger
from searchtools.utils.error_handling import handle_ui_operation
import config


class ResultManager:
    """
    Manages search results display, storage, and export functionality.
    """
    
    def __init__(self, results_frame_manager, root_window):
        """
        Initialize the result manager.
        
        Args:
            results_frame_manager: ResultsFrameManager instance
            root_window: Root window for dialogs
        """
        self.results_frame_manager = results_frame_manager
        self.root_window = root_window
        self.results = []
        
        # Result queue for thread-safe updates
        self.result_queue = queue.Queue()
        self.processed_result_ids = set()
        self.update_timer = None
        
        # Callbacks
        self.state_update_callback = None
    
    def set_state_update_callback(self, callback):
        """
        Set callback for state updates (e.g., button state changes).
        
        Args:
            callback: Callback function for state updates
        """
        self.state_update_callback = callback
    
    def add_result(self, result):
        """
        Add a search result to the queue for processing.
        This method is thread-safe and can be called from scanner threads.
        
        Args:
            result (SearchResult): Search result to add
        """
        # Skip archive markers
        if result.keyword == config.ARCHIVE_MARKER_KEYWORD:
            return
        
        # Log the result for debugging
        logger.info(f"Match in text {result.file} [Line {result.location}]: {result.keyword}")
        
        # Create a unique identifier for this result to avoid duplicates
        result_id = f"{result.file}:{result.location}:{result.keyword}"
        
        # Store the result with its unique ID in a tuple
        self.result_queue.put((result_id, result))
        
        # Schedule UI update if not already scheduled
        self._schedule_ui_update()
    
    def _schedule_ui_update(self):
        """Schedule a UI update to process results in the queue."""
        if self.update_timer is None:
            # Schedule an update after a short delay to batch multiple results
            self.update_timer = self.root_window.after(
                config.UI_UPDATE_DELAY, 
                self._process_result_queue
            )
    
    def _process_result_queue(self):
        """Process results from the queue and update the UI."""
        # Reset the timer
        self.update_timer = None
        
        # Process results in batches for better performance
        results_to_process = []
        batch_size = config.UI_BATCH_SIZE
        
        # Get results from the queue
        for _ in range(batch_size):
            try:
                result_id, result = self.result_queue.get_nowait()
                
                # Check for duplicates
                if result_id not in self.processed_result_ids:
                    self.processed_result_ids.add(result_id)
                    results_to_process.append(result)
                    self.results.append(result)
                
            except queue.Empty:
                break
        
        # Add results to the treeview
        last_item_id = None
        if results_to_process:
            last_item_id = self.results_frame_manager.add_results_batch(results_to_process)
        
        # Scroll to the last item
        if last_item_id:
            self.results_frame_manager.scroll_to_item(last_item_id)
        
        # Update button states
        if self.state_update_callback and self.results:
            self.state_update_callback(has_results=True)
        
        # Update the UI
        self.root_window.update_idletasks()
        
        # If there are more results in the queue, schedule another update
        if not self.result_queue.empty():
            self._schedule_ui_update()
    
    def clear_results(self):
        """Clear all search results."""
        # Clear the treeview
        self.results_frame_manager.clear_results()
        
        # Clear the results list and queue
        self.results = []
        
        # Clear the queue
        while not self.result_queue.empty():
            try:
                self.result_queue.get_nowait()
            except queue.Empty:
                break
        
        # Clear the processed result IDs set
        self.processed_result_ids.clear()
        
        # Cancel any pending update timer
        if self.update_timer is not None:
            self.root_window.after_cancel(self.update_timer)
            self.update_timer = None
        
        # Update button states
        if self.state_update_callback:
            self.state_update_callback(has_results=False)
        
        # Log
        logger.info("Results cleared")
    
    def get_selected_result(self):
        """
        Get the currently selected result.
        
        Returns:
            SearchResult: Selected result, or None if no selection
        """
        selected_index = self.results_frame_manager.get_selected_result_index()
        if selected_index is not None and 0 <= selected_index < len(self.results):
            return self.results[selected_index]
        return None
    
    def get_results_count(self):
        """
        Get the number of results.
        
        Returns:
            int: Number of results
        """
        return len(self.results)
    
    @handle_ui_operation(error_message="Error exporting results")
    def export_results(self):
        """Export the search results to a file."""
        if not self.results:
            messagebox.showwarning("Warning", "No results to export")
            return
        
        # Ask user for file location and format
        file_path = filedialog.asksaveasfilename(
            title="Export Results",
            defaultextension=".csv",
            filetypes=[
                ("CSV files", "*.csv"),
                ("Excel files", "*.xlsx"),
                ("All files", "*.*")
            ]
        )
        
        if not file_path:
            return
        
        try:
            # Determine export format based on file extension
            _, ext = os.path.splitext(file_path.lower())
            
            if ext == ".xlsx":
                self._export_to_excel(file_path)
            else:
                self._export_to_csv(file_path)
            
            messagebox.showinfo("Success", f"Results exported to {file_path}")
            logger.info(f"Results exported to {file_path}")
            
        except Exception as e:
            error_msg = f"Failed to export results: {str(e)}"
            messagebox.showerror("Error", error_msg)
            logger.error(error_msg)
    
    def _export_to_csv(self, file_path):
        """
        Export results to a CSV file.
        
        Args:
            file_path (str): Path to the CSV file
        """
        with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            
            # Write header
            writer.writerow(["File", "Path", "Keyword", "Location", "Context"])
            
            # Write results
            for result in self.results:
                writer.writerow([
                    result.file,
                    result.path,
                    result.keyword,
                    result.location,
                    result.context
                ])
    
    def _export_to_excel(self, file_path):
        """
        Export results to an Excel file.
        
        Args:
            file_path (str): Path to the Excel file
        """
        try:
            import openpyxl
            from openpyxl.styles import Font, PatternFill
            
            # Create workbook and worksheet
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "Search Results"
            
            # Write header with styling
            headers = ["File", "Path", "Keyword", "Location", "Context"]
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
            
            # Write results
            for row, result in enumerate(self.results, 2):
                ws.cell(row=row, column=1, value=result.file)
                ws.cell(row=row, column=2, value=result.path)
                ws.cell(row=row, column=3, value=result.keyword)
                ws.cell(row=row, column=4, value=result.location)
                ws.cell(row=row, column=5, value=result.context)
            
            # Auto-adjust column widths
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)  # Cap at 50 characters
                ws.column_dimensions[column_letter].width = adjusted_width
            
            # Save the workbook
            wb.save(file_path)
            
        except ImportError:
            # Fall back to CSV if openpyxl is not available
            logger.warning("openpyxl not available, falling back to CSV export")
            csv_path = file_path.replace('.xlsx', '.csv')
            self._export_to_csv(csv_path)
            raise Exception(f"Excel export requires openpyxl. Results saved as CSV: {csv_path}")


class ResultDetailsManager:
    """
    Manages the display of detailed result information in popup windows.
    """
    
    def __init__(self, root_window):
        """
        Initialize the result details manager.
        
        Args:
            root_window: Root window for creating dialogs
        """
        self.root_window = root_window
    
    @handle_ui_operation(error_message="Error opening result details window")
    def show_result_details(self, result):
        """
        Show detailed information for a search result.
        
        Args:
            result (SearchResult): The result to display details for
        """
        if not result:
            return
        
        # Create the window and main frame
        details_window, details_frame = self._create_details_window(result)
        
        # Create file information section
        self._create_file_info_section(details_frame, result)
        
        # Create match information section
        self._create_match_info_section(details_frame, result)
        
        # Create full context section
        self._create_context_section(details_frame, result)
        
        # Add a close button
        close_button = ttk.Button(details_frame, text="Close", command=details_window.destroy)
        close_button.pack(pady=10)
    
    def _create_details_window(self, result):
        """
        Create the details window and main frame.
        
        Args:
            result (SearchResult): The result to display
            
        Returns:
            tuple: (details_window, details_frame)
        """
        # Create a new top-level window
        details_window = tk.Toplevel(self.root_window)
        details_window.title(f"Result Details - {result.file}")
        details_window.geometry(config.DETAILS_WINDOW_SIZE)
        details_window.transient(self.root_window)
        details_window.grab_set()
        
        # Create a frame for the details
        details_frame = ttk.Frame(details_window, padding=10)
        details_frame.pack(fill=tk.BOTH, expand=True)
        
        return details_window, details_frame
    
    def _create_file_info_section(self, parent_frame, result):
        """Create the file information section."""
        file_info_frame = ttk.LabelFrame(parent_frame, text="File Information")
        file_info_frame.pack(fill=tk.X, pady=5)
        
        # File name
        file_label = ttk.Label(file_info_frame, text=f"File: {result.file}", foreground="white")
        file_label.pack(anchor=tk.W, padx=5, pady=2)
        
        # Full path
        path_label = ttk.Label(file_info_frame, text=f"Path: {result.path}", foreground="white")
        path_label.pack(anchor=tk.W, padx=5, pady=2)
    
    def _create_match_info_section(self, parent_frame, result):
        """Create the match information section."""
        match_info_frame = ttk.LabelFrame(parent_frame, text="Match Information")
        match_info_frame.pack(fill=tk.X, pady=5)
        
        # Keyword
        keyword_label = ttk.Label(match_info_frame, text=f"Keyword: {result.keyword}", foreground="white")
        keyword_label.pack(anchor=tk.W, padx=5, pady=2)
        
        # Location
        location_label = ttk.Label(match_info_frame, text=f"Location: {result.location}", foreground="white")
        location_label.pack(anchor=tk.W, padx=5, pady=2)
        
        # Context
        context_label = ttk.Label(match_info_frame, text=f"Context: {result.context}", foreground="white")
        context_label.pack(anchor=tk.W, padx=5, pady=2)
    
    def _create_context_section(self, parent_frame, result):
        """Create the full context section."""
        context_frame = ttk.LabelFrame(parent_frame, text="Full Context")
        context_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # Create text widget with scrollbar
        context_text = tk.Text(context_frame, wrap=tk.WORD, height=15, foreground="white")
        context_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Add scrollbar
        scrollbar = ttk.Scrollbar(context_frame, command=context_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        context_text.configure(yscrollcommand=scrollbar.set)
        
        # Configure text colors
        context_text.configure(insertbackground="black")
        context_text.tag_configure("default", foreground="black")
        
        # Insert the context
        context_text.insert(tk.END, result.full_context, "default")
        
        # Highlight the keyword
        self._highlight_text(context_text, result.keyword)
        
        # Make read-only
        context_text.configure(state=tk.DISABLED)
    
    def _highlight_text(self, text_widget, keyword):
        """Highlight all occurrences of a keyword in a text widget."""
        # Configure highlight tag
        text_widget.tag_configure("highlight", background="yellow", foreground="black")
        
        # Search for keyword and highlight
        start_pos = "1.0"
        while True:
            pos = text_widget.search(keyword, start_pos, tk.END, nocase=True)
            if not pos:
                break
            
            end_pos = f"{pos}+{len(keyword)}c"
            text_widget.tag_add("highlight", pos, end_pos)
            start_pos = end_pos
