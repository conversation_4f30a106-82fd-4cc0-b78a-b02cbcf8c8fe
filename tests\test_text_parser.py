"""
Test script for TextParser refactoring
"""

import os
import tempfile
from searchtools.parsers.text_parser import <PERSON><PERSON><PERSON><PERSON>

def test_text_parser():
    """Test the TextParser class"""
    # Create a temporary test file
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
        f.write("This is a test file\n")
        f.write("It contains the keyword hello\n")
        f.write("And another keyword world\n")
        temp_file = f.name
    
    try:
        # Test the parse method
        results = TextParser.parse(
            temp_file, 
            os.path.basename(temp_file), 
            ["hello", "world"], 
            case_sensitive=False
        )
        
        print(f"Parse results: {len(results)} matches found")
        for result in results:
            print(f"  - Found '{result.keyword}' in context: '{result.context}'")
        
        # Test the parse_enhanced method
        enhanced_results = TextParser.parse_enhanced(
            temp_file, 
            os.path.basename(temp_file), 
            ["hello", "world"], 
            case_sensitive=False
        )
        
        print(f"Parse enhanced results: {len(enhanced_results)} matches found")
        for result in enhanced_results:
            print(f"  - Found '{result.keyword}' in context: '{result.context}'")
            
        # Verify both methods return the same results
        if len(results) == len(enhanced_results):
            print("✅ Both methods returned the same number of results")
        else:
            print("❌ Methods returned different numbers of results")
            
    finally:
        # Clean up
        os.unlink(temp_file)

if __name__ == "__main__":
    test_text_parser()
