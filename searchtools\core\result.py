"""
Result data structure for search results
"""

from typing import Di<PERSON>, <PERSON><PERSON>, Optional, Any
import config
from searchtools.utils.logging import logger

class SearchResult:
    """
    Class representing a search result.
    """
    def __init__(self, file: str, path: str, keyword: str, context: str,
                location: str = "", full_context: Optional[str] = None):
        """
        Initialize a search result.

        Args:
            file (str): File name
            path (str): Full file path
            keyword (str): Found keyword
            context (str): Context around the keyword
            location (str, optional): Location within the file. Defaults to "".
            full_context (str, optional): Full context for detailed view. Defaults to None.

        Raises:
            ValueError: If required parameters are empty or None
        """
        # Validate required parameters
        self._validate_parameter("file", file)
        self._validate_parameter("path", path)
        self._validate_parameter("keyword", keyword)
        self._validate_parameter("context", context)

        # Assign values
        self.file: str = file
        self.path: str = path
        self.keyword: str = keyword
        self.context: str = context
        self.location: str = location or ""  # Ensure location is never None
        self.full_context: str = full_context if full_context is not None else context

    @staticmethod
    def _validate_parameter(name: str, value: Any) -> None:
        """
        Validate that a parameter is not None or empty.

        Args:
            name (str): Parameter name for error message
            value (Any): Parameter value to validate

        Raises:
            ValueError: If the parameter is None or empty string
        """
        if value is None:
            raise ValueError(f"Parameter '{name}' cannot be None")

        if isinstance(value, str) and not value.strip():
            raise ValueError(f"Parameter '{name}' cannot be empty")

    def to_dict(self) -> Dict[str, str]:
        """
        Convert the result to a dictionary.

        Returns:
            dict: Dictionary representation of the result
        """
        return {
            "file": self.file,
            "path": self.path,
            "keyword": self.keyword,
            "context": self.context,
            "location": self.location,
            "full_context": self.full_context
        }

    def get_display_context(self, max_length: Optional[int] = None) -> str:
        """
        Get a truncated context for display.

        Args:
            max_length (int, optional): Maximum length of the context.
                Defaults to config.DISPLAY_CONTEXT_MAX_LENGTH.

        Returns:
            str: Truncated context with optional location information
        """
        # Use config value if max_length is not provided
        if max_length is None:
            max_length = config.DISPLAY_CONTEXT_MAX_LENGTH

        # Validate max_length
        if max_length <= 0:
            logger.warning(f"Invalid max_length ({max_length}), using default value")
            max_length = config.DISPLAY_CONTEXT_MAX_LENGTH

        # Build the result string efficiently
        result = []

        # Add location if provided
        if self.location:
            result.append("[")
            result.append(self.location)
            result.append("] ")

        # Add truncated context
        if len(self.context) > max_length:
            result.append(self.context[:max_length])
            result.append("...")
        else:
            result.append(self.context)

        # Join all parts into a single string (more efficient than multiple concatenations)
        return "".join(result)

    def get_tree_values(self) -> Tuple[str, str, str, str]:
        """
        Get values for display in a treeview.

        Returns:
            tuple: (file, path, keyword, display_context)
        """
        return (self.file, self.path, self.keyword, self.get_display_context())
