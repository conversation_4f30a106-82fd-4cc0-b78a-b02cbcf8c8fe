2025-05-04 21:28:39,631 - INFO - PyPDF2 configured. PDF parsing support enabled.
2025-05-04 21:28:39,662 - INFO - win32com configured. DOC parsing support enabled.
2025-05-04 21:28:39,669 - DEBUG - antiword not available as fallback for DOC files.
2025-05-04 21:28:39,953 - INFO - Application started
2025-05-04 21:28:39,955 - INFO - Custom styling applied
2025-05-04 21:28:39,966 - INFO - rarfile configured. RAR parsing support enabled.
2025-05-04 21:28:40,230 - INFO - py7zr configured. 7z archive parsing support enabled.
2025-05-04 21:28:40,231 - DEBUG - Registered parser: Archive Parser
2025-05-04 21:28:40,234 - DEBUG - Registered parser: CSV Parser
2025-05-04 21:28:40,235 - DEBUG - Registered parser: DOC Parser
2025-05-04 21:28:40,235 - DEBUG - Registered parser: DOCX Parser
2025-05-04 21:28:40,237 - DEBUG - Registered parser: Excel Parser
2025-05-04 21:28:40,238 - DEBUG - Registered parser: ODS Parser
2025-05-04 21:28:40,239 - DEBUG - Registered parser: PDF Parser
2025-05-04 21:28:40,239 - DEBUG - Registered parser: Text Parser
2025-05-04 21:28:40,239 - INFO - Discovered 8 parser plugins
2025-05-04 21:28:40,292 - INFO - Looking for logo at (method 1): c:\Users\<USER>\Desktop\L457\SearchTools-v1\icon.ico
2025-05-04 21:28:40,294 - INFO - Logo file found at: c:\Users\<USER>\Desktop\L457\SearchTools-v1\icon.ico
2025-05-04 21:28:40,471 - INFO - Image opened, mode: RGBA, size: (159, 31)
2025-05-04 21:28:40,472 - INFO - Resized image to 100x19 to maintain aspect ratio
2025-05-04 21:28:40,482 - INFO - Converted to PhotoImage
2025-05-04 21:28:40,493 - INFO - Bottom logo loaded successfully
2025-05-04 21:28:40,792 - INFO - UI initialized
2025-05-04 21:29:24,744 - INFO - Window closed by user
2025-05-04 21:29:24,982 - INFO - Application exited
