"""
Configuration settings for the SearchTools application
"""

import os

# Application settings
APP_NAME = "Dataset Tool by Techlab CTI"
APP_VERSION = "V1"
DEFAULT_WINDOW_SIZE = "1200x800"

# UI Theme colors
UI_COLORS = {
    # Base colors
    "bg_color": "#0f172a",        # Deep blue-black background
    "fg_color": "#f1f5f9",        # Light text (off-white with slight blue tint)
    "accent_color": "#3b82f6",    # Vibrant blue accent
    "secondary_color": "#1e293b",  # Slightly lighter background
    "tertiary_color": "#334155",   # Even lighter for highlights

    # Status colors
    "success_color": "#10b981",   # Emerald green for success
    "warning_color": "#f59e0b",   # Amber for warnings
    "error_color": "#ef4444",     # Red for errors
    "info_color": "#06b6d4",      # Cyan for information

    # Accent colors
    "purple_accent": "#8b5cf6",   # Violet accent
    "pink_accent": "#ec4899",     # Pink accent
    "yellow_accent": "#eab308",   # Yellow accent
    "teal_accent": "#14b8a6",     # Teal accent
    "indigo_accent": "#1e40af",   # Darker indigo accent for header

    # UI elements
    "border_radius": 12,          # Border radius
    "card_shadow": "0px 8px 24px rgba(0, 0, 0, 0.25)",  # Shadow for cards
    "glass_bg": "rgba(30, 41, 59, 0.7)",  # Semi-transparent background
    "border_color": "#475569",    # Border color for separators
    "hover_color": "#4b5563",     # Color for hover states
    "active_color": "#3b82f6"     # Color for active states
}

# File processing settings
MAX_WORKERS = min(os.cpu_count() or 4, 8)  # Max 8 threads or CPU count, whichever is smaller
LARGE_FILE_SIZE = 100 * 1024 * 1024  # 100MB
CHUNK_SIZE = 50000  # For chunked reading
BATCH_SIZE = 20  # Smaller batch size for more frequent UI updates

# Progress tracking settings
PROGRESS_START = 0
PROGRESS_COMPLETE = 100
PROGRESS_ARCHIVE_MARKING = 50
PROGRESS_FILE_INIT = 20
PROGRESS_PARSER_FOUND = 40
PROGRESS_PARSING_STARTED = 60
PROGRESS_PARSING_COMPLETE = 80
PROGRESS_ARCHIVE_EXTRACTION = 30

# Status update settings
STATUS_UPDATE_FREQUENCY = 5  # Update status more frequently (every N files)
STATUS_DELAY = 0.5  # Delay in seconds for status updates

# UI update settings for real-time results
UI_UPDATE_BATCH_SIZE = 10  # Number of results to accumulate before updating UI
UI_UPDATE_THROTTLE_MS = 100  # Minimum time between UI updates in milliseconds
UI_MAX_UPDATES_PER_SEC = 5  # Maximum number of UI updates per second

# Archive settings
ARCHIVE_EXTENSIONS = ('.zip', '.rar', '.7z')
ARCHIVE_MARKER_KEYWORD = "ARCHIVE_MARKER"
ESTIMATED_FILES_PER_ARCHIVE = 10  # Estimated number of files per archive for progress calculation

# Security settings for archive extraction
ARCHIVE_SECURITY = {
    # Maximum total size of extracted files (in bytes) - 1GB default
    "max_extracted_size": 1024 * 1024 * 1024,

    # Maximum number of files that can be extracted from a single archive
    "max_files_per_archive": 10000,

    # Maximum size of individual file in archive (in bytes) - 500MB default
    "max_individual_file_size": 500 * 1024 * 1024,

    # Maximum depth of directory nesting allowed
    "max_directory_depth": 20,

    # Blocked file extensions for security (case-insensitive)
    "blocked_extensions": ['.exe', '.bat', '.cmd', '.com', '.scr', '.pif', '.vbs', '.js'],

    # Enable strict path validation (recommended: True)
    "strict_path_validation": True,

    # Allow extraction of files with no extension
    "allow_no_extension": True,

    # Maximum length of file paths
    "max_path_length": 260  # Windows MAX_PATH limitation
}

# Default values
DEFAULT_ESTIMATION_FALLBACK = 1000  # Default value if file estimation fails

# Logging settings
LOG_DIR = "logs"
LOG_FORMAT = "%(asctime)s - %(levelname)s - %(message)s"
LOG_MAX_SIZE = 10 * 1024 * 1024  # 10MB
LOG_BACKUP_COUNT = 5

# History settings
MAX_HISTORY_ENTRIES = 50  # Maximum number of search history entries to keep

# Result display settings
DISPLAY_CONTEXT_MAX_LENGTH = 100  # Maximum length for displayed context in search results

# Timeout settings
WORD_INIT_TIMEOUT = 5  # Timeout in seconds for Word initialization
DOC_OPEN_TIMEOUT = 5  # Timeout in seconds for document opening
TEXT_EXTRACT_TIMEOUT = 5  # Timeout in seconds for text extraction
STOP_SCAN_TIMEOUT = 20.0  # Timeout in seconds for stopping a scan (increased for better handling of large files)
TIMEOUT_SLEEP_INTERVAL = 0.1  # Sleep interval in seconds for timeout loops

# File size thresholds
MIN_DOC_FILE_SIZE = 100  # Minimum size in bytes for a valid DOC file
MIN_EXCEL_FILE_SIZE = 100  # Minimum size in bytes for a valid Excel file
LARGE_CSV_FILE_THRESHOLD = 100 * 1024 * 1024  # 100MB threshold for large CSV files

# UI window dimensions
DETAILS_WINDOW_SIZE = "900x650"  # Size for the details window
EXPORT_WINDOW_SIZE = "550x350"  # Size for the export window

# UI element settings
EXCEL_COLUMN_WIDTH_LIMIT = 50  # Maximum width for Excel columns when exporting
TREE_ROW_HEIGHT = 36  # Height for rows in treeview widgets
HIGHLIGHT_COLOR = "#ffcc00"  # Color for highlighting matched text (bright yellow)

# UI padding and spacing
UI_MAIN_PADDING = 10  # Main padding for frames
UI_SECTION_PADDING = 5  # Padding for sections within frames
UI_ELEMENT_PADDING = 2  # Padding for individual elements
UI_BUTTON_PADDING = 10  # Padding for buttons

# UI dimensions
UI_MIN_WINDOW_WIDTH = 800  # Minimum window width
UI_MIN_WINDOW_HEIGHT = 600  # Minimum window height
UI_MATCH_TEXT_HEIGHT = 3  # Height for match text widgets

# UI animation settings
UI_ANIMATION_INTERVAL = 0.5  # Interval for animations in seconds

# UI font settings
UI_FONT_FAMILY = "TkDefaultFont"  # Default font family
UI_FONT_SIZE_NORMAL = 10  # Normal font size
UI_FONT_SIZE_LARGE = 12  # Large font size
UI_FONT_SIZE_SMALL = 9  # Small font size

# Temporary directory security settings
TEMP_DIR_SECURITY = {
    # Maximum number of temporary directories to keep at once
    "max_temp_dirs": 100,

    # Maximum age of temporary directories before forced cleanup (in seconds)
    "max_temp_dir_age": 3600,  # 1 hour

    # Enable automatic cleanup on application exit
    "cleanup_on_exit": True,

    # Enable periodic cleanup during operation
    "periodic_cleanup": True,

    # Interval for periodic cleanup (in seconds)
    "cleanup_interval": 300,  # 5 minutes

    # Prefix for temporary directories (for identification)
    "temp_dir_prefix": "searchtools_secure_",

    # Enable secure deletion (overwrite files before deletion)
    "secure_deletion": False  # Disabled by default for performance
}
