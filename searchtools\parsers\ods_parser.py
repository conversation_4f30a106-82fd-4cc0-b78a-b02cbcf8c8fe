"""
Parser for ODS files (OpenDocument Spreadsheet)
"""

from typing import List

from searchtools.utils.logging import logger
from searchtools.core.searcher import TextSearcher
from searchtools.core.result import SearchResult
from searchtools.parsers.base_parser import BaseParser

# Check if pyexcel-ods is available
PYEXCEL_ODS_AVAILABLE = False
try:
    import pyexcel_ods
    PYEXCEL_ODS_AVAILABLE = True
except ImportError:
    logger.warning("pyexcel-ods not available. ODS support disabled.")

# Check if ezodf is available as an alternative
EZODF_AVAILABLE = False
try:
    import ezodf
    EZODF_AVAILABLE = True
except ImportError:
    logger.debug("ezodf not available as alternative for ODS files.")

class OdsParser(BaseParser):
    """
    Parser for ODS files (OpenDocument Spreadsheet).
    """

    @classmethod
    def get_name(cls) -> str:
        """
        Get the name of the parser.

        Returns:
            str: Parser name
        """
        return "ODS Parser"

    @classmethod
    def get_supported_extensions(cls) -> List[str]:
        """
        Get the list of file extensions supported by this parser.

        Returns:
            List[str]: List of supported file extensions
        """
        return ['.ods']

    @classmethod
    def get_priority(cls) -> int:
        """
        Get the priority of the parser.

        Returns:
            int: Priority value
        """
        return 10

    @classmethod
    def is_available(cls) -> bool:
        """
        Check if ODS parsing is available.

        Returns:
            bool: True if ODS parsing is available, False otherwise
        """
        return PYEXCEL_ODS_AVAILABLE or EZODF_AVAILABLE

    @classmethod
    def parse(cls, file_path: str, file_name: str, keywords: List[str], case_sensitive: bool = False,
             use_regex: bool = False, whole_word: bool = False) -> List[SearchResult]:
        """
        Parse an ODS file and search for keywords.

        Args:
            file_path (str): Path to the file
            file_name (str): Name of the file
            keywords (list): List of keywords to search for
            case_sensitive (bool, optional): Whether to use case-sensitive search. Defaults to False.
            use_regex (bool, optional): Whether to treat keywords as regular expressions. Defaults to False.
            whole_word (bool, optional): Whether to match whole words only. Defaults to False.

        Returns:
            list: List of SearchResult objects
        """
        if not cls.is_available():
            logger.warning(f"ODS parsing not available for {file_name}")
            return []

        results = []

        # Try pyexcel-ods first if available
        if PYEXCEL_ODS_AVAILABLE:
            try:
                data = pyexcel_ods.get_data(file_path)
                for sheet_name, sheet_data in data.items():
                    for row_idx, row in enumerate(sheet_data):
                        for col_idx, cell in enumerate(row):
                            if cell and isinstance(cell, (str, int, float)):
                                cell_text = str(cell).strip()
                                if cell_text:
                                    found_keyword = TextSearcher.search_text(cell_text, keywords, case_sensitive, use_regex, whole_word)
                                    if found_keyword:
                                        logger.info(f"Match in ODS {file_name} [Sheet: {sheet_name}, Cell: {cls._get_column_letter(col_idx)}{row_idx+1}]: '{found_keyword}'")
                                        results.append(
                                            SearchResult(
                                                file=file_name,
                                                path=file_path,
                                                keyword=found_keyword,
                                                context=cell_text,
                                                location=f"ODS: {sheet_name} - {cls._get_column_letter(col_idx)}{row_idx+1}",
                                                full_context=cell_text
                                            )
                                        )
                return results
            except Exception as e:
                logger.warning(f"pyexcel-ods failed for {file_name}: {e}")

        # Fall back to ezodf if pyexcel-ods failed
        if EZODF_AVAILABLE:
            try:
                doc = ezodf.opendoc(file_path)
                for sheet in doc.sheets:
                    sheet_name = sheet.name
                    for row_idx, row in enumerate(sheet.rows()):
                        for col_idx, cell in enumerate(row):
                            if cell.value is not None:
                                cell_text = str(cell.value).strip()
                                if cell_text:
                                    found_keyword = TextSearcher.search_text(cell_text, keywords, case_sensitive, use_regex, whole_word)
                                    if found_keyword:
                                        logger.info(f"Match in ODS {file_name} [Sheet: {sheet_name}, Cell: {cls._get_column_letter(col_idx)}{row_idx+1}]: '{found_keyword}'")
                                        results.append(
                                            SearchResult(
                                                file=file_name,
                                                path=file_path,
                                                keyword=found_keyword,
                                                context=cell_text,
                                                location=f"ODS: {sheet_name} - {cls._get_column_letter(col_idx)}{row_idx+1}",
                                                full_context=cell_text
                                            )
                                        )
                return results
            except Exception as e:
                logger.warning(f"ezodf failed for {file_name}: {e}")

        logger.warning(f"All ODS parsing methods failed for {file_name}")
        return []

    @staticmethod
    def _get_column_letter(col_idx):
        """
        Convert a column index to a column letter (A, B, C, ..., Z, AA, AB, etc.)

        Args:
            col_idx (int): Column index (0-based)

        Returns:
            str: Column letter
        """
        result = ""
        while col_idx >= 0:
            result = chr(col_idx % 26 + 65) + result
            col_idx = col_idx // 26 - 1
        return result
