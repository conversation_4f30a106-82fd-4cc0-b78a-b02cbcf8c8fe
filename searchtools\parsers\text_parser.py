"""
Parser for text files
"""

from typing import List, Tuple, Optional, Any

from searchtools.utils.logging import logger
from searchtools.utils.logging_guidelines import (
    log_parser_error, log_file_error, log_search_match
)
from searchtools.utils.error_handling import handle_parser_error
from searchtools.utils.file_utils import safe_read_text
from searchtools.core.searcher import TextSearcher
from searchtools.core.result import SearchResult
from searchtools.core.result_factory import SearchResultFactory
from searchtools.parsers.base_parser import BaseParser

class TextParser(BaseParser):
    """
    Parser for text files.
    """

    @classmethod
    def _create_search_result(cls, file_name: str, file_path: str, keyword: str,
                             context: str, location_type: str, location_detail: Any) -> SearchResult:
        """
        Create a standardized SearchResult object using the factory.

        Args:
            file_name (str): Name of the file
            file_path (str): Path to the file
            keyword (str): Found keyword
            context (str): Context text around the keyword
            location_type (str): Type of location (e.g., "Text", "Binary")
            location_detail (Any): Detail about the location (e.g., line number, offset)

        Returns:
            SearchResult: A search result object
        """
        # Use appropriate factory method based on location type
        if location_type == "Text" and isinstance(location_detail, str) and location_detail.startswith("Line "):
            # Extract line number for text results
            try:
                line_number = int(location_detail.replace("Line ", ""))
                return SearchResultFactory.create_text_result(
                    file_name=file_name,
                    file_path=file_path,
                    keyword=keyword,
                    line_number=line_number,
                    line_content=context
                )
            except ValueError:
                # Fall back to generic if line number parsing fails
                pass
        elif location_type == "Binary":
            # For binary results, location_detail should be the offset
            try:
                offset = int(location_detail) if isinstance(location_detail, (int, str)) else 0
                return SearchResultFactory.create_binary_result(
                    file_name=file_name,
                    file_path=file_path,
                    keyword=keyword,
                    offset=offset,
                    surrounding_text=context
                )
            except (ValueError, TypeError):
                # Fall back to generic if offset parsing fails
                pass

        # Fall back to generic result for other cases
        return SearchResultFactory.create_generic_result(
            file_name=file_name,
            file_path=file_path,
            keyword=keyword,
            location_type=location_type,
            location_detail=str(location_detail),
            context=context
        )

    @classmethod
    def _search_text_content(cls, text: str, file_path: str, file_name: str,
                            keywords: List[str], location_type: str, location_detail: Any,
                            case_sensitive: bool = False, use_regex: bool = False,
                            whole_word: bool = False) -> Optional[SearchResult]:
        """
        Search text content for keywords and create a SearchResult if found.

        Args:
            text (str): Text content to search
            file_path (str): Path to the file
            file_name (str): Name of the file
            keywords (list): List of keywords to search for
            location_type (str): Type of location (e.g., "Text", "Binary")
            location_detail (Any): Detail about the location (e.g., line number, offset)
            case_sensitive (bool, optional): Whether to use case-sensitive search. Defaults to False.
            use_regex (bool, optional): Whether to treat keywords as regular expressions. Defaults to False.
            whole_word (bool, optional): Whether to match whole words only. Defaults to False.

        Returns:
            Optional[SearchResult]: A search result object if a match is found, None otherwise
        """
        # Skip empty text
        if not text.strip():
            return None

        # Search for keywords
        found_keyword = TextSearcher.search_text(
            text.strip(), keywords, case_sensitive, use_regex, whole_word
        )

        # If a keyword is found, create and return a search result
        if found_keyword:
            context = text.strip()
            log_search_match(location_type, file_name, str(location_detail), found_keyword)
            return cls._create_search_result(
                file_name, file_path, found_keyword, context, location_type, location_detail
            )

        return None

    @classmethod
    def get_name(cls) -> str:
        """
        Get the name of the parser.

        Returns:
            str: Parser name
        """
        return "Text Parser"

    @classmethod
    def get_supported_extensions(cls) -> List[str]:
        """
        Get the list of file extensions supported by this parser.

        Returns:
            List[str]: List of supported file extensions
        """
        return ['.txt', '.log', '.md', '.csv', '.json', '.xml', '.html', '.htm', '.css', '.js', '.py', '.java', '.c', '.cpp', '.h', '.hpp']

    @classmethod
    def is_available(cls) -> bool:
        """
        Check if the parser is available.

        Returns:
            bool: True if the parser is available, False otherwise
        """
        return True

    @classmethod
    def get_priority(cls) -> int:
        """
        Get the priority of the parser.

        Returns:
            int: Priority value
        """
        return -10  # Lowest priority as this is a fallback parser

    @classmethod
    def _parse_text_lines(cls, lines, file_path: str, file_name: str, keywords: List[str],
                         case_sensitive: bool = False, use_regex: bool = False,
                         whole_word: bool = False) -> Tuple[List[SearchResult], bool]:
        """
        Parse text lines and search for keywords.

        Args:
            lines: List of tuples (line_number, line_text)
            file_path (str): Path to the file
            file_name (str): Name of the file
            keywords (list): List of keywords to search for
            case_sensitive (bool, optional): Whether to use case-sensitive search. Defaults to False.
            use_regex (bool, optional): Whether to treat keywords as regular expressions. Defaults to False.
            whole_word (bool, optional): Whether to match whole words only. Defaults to False.

        Returns:
            Tuple[List[SearchResult], bool]: List of search results and success flag
        """
        results = []
        success = False

        for line_num, line_text in lines:
            # Search the line for keywords
            result = cls._search_text_content(
                line_text, file_path, file_name, keywords,
                "Text", f"Line {line_num}",
                case_sensitive, use_regex, whole_word
            )

            # If a match was found, add it to the results
            if result:
                results.append(result)
                success = True

        return results, success

    @classmethod
    def _search_binary_content(cls, binary_data: bytes, file_path: str, file_name: str,
                              keywords: List[str], case_sensitive: bool = False,
                              use_regex: bool = False, whole_word: bool = False) -> List[SearchResult]:
        """
        Search binary data for keywords using various encodings.

        Args:
            binary_data (bytes): Binary data to search
            file_path (str): Path to the file
            file_name (str): Name of the file
            keywords (list): List of keywords to search for
            case_sensitive (bool, optional): Whether to use case-sensitive search. Defaults to False.
            use_regex (bool, optional): Whether to treat keywords as regular expressions. Defaults to False.
            whole_word (bool, optional): Whether to match whole words only. Defaults to False.

        Returns:
            List[SearchResult]: List of search results
        """
        results = []

        # Try different encodings to convert binary data to text
        for encoding in ['utf-8', 'latin-1', 'cp1252', 'ascii']:
            try:
                # Decode binary data using the current encoding
                text = binary_data.decode(encoding, errors='replace')

                # Use TextSearcher to find matches
                found_keyword = TextSearcher.search_text(
                    text, keywords, case_sensitive, use_regex, whole_word
                )

                if found_keyword:
                    # Find the match index for context extraction
                    search_text = text.lower() if not case_sensitive else text
                    search_keyword = found_keyword.lower() if not case_sensitive else found_keyword

                    # Find the match index (simple approach for all cases)
                    match_index = search_text.find(search_keyword)

                    # Extract context around the match
                    start = max(0, match_index - 50)
                    end = min(len(text), match_index + len(search_keyword) + 50)
                    context = text[start:end].replace('\r', ' ').replace('\n', ' ')

                    # Create a search result
                    location_detail = f"Offset {match_index} ({encoding})"
                    log_search_match("Binary", file_name, location_detail, found_keyword)
                    results.append(cls._create_search_result(
                        file_name, file_path, found_keyword, context,
                        "Binary", location_detail
                    ))

                    # We found a match with this encoding, no need to try others
                    break

            except Exception:
                # If this encoding fails, try the next one
                continue

        return results

    @classmethod
    @handle_parser_error
    def parse(cls, file_path: str, file_name: str, keywords: List[str], case_sensitive: bool = False,
             use_regex: bool = False, whole_word: bool = False) -> List[SearchResult]:
        """
        Parse a text file and search for keywords.

        Args:
            file_path (str): Path to the file
            file_name (str): Name of the file
            keywords (list): List of keywords to search for
            case_sensitive (bool, optional): Whether to use case-sensitive search. Defaults to False.
            use_regex (bool, optional): Whether to treat keywords as regular expressions. Defaults to False.
            whole_word (bool, optional): Whether to match whole words only. Defaults to False.

        Returns:
            list: List of SearchResult objects
        """
        lines = safe_read_text(file_path)
        results, _ = cls._parse_text_lines(lines, file_path, file_name, keywords,
                                         case_sensitive, use_regex, whole_word)
        return results

    @classmethod
    @handle_parser_error
    def parse_enhanced(cls, file_path: str, file_name: str, keywords: List[str], case_sensitive: bool = False,
                      use_regex: bool = False, whole_word: bool = False) -> List[SearchResult]:
        """
        Parse a text file with enhanced encoding detection and search for keywords.

        Args:
            file_path (str): Path to the file
            file_name (str): Name of the file
            keywords (list): List of keywords to search for
            case_sensitive (bool, optional): Whether to use case-sensitive search. Defaults to False.
            use_regex (bool, optional): Whether to treat keywords as regular expressions. Defaults to False.
            whole_word (bool, optional): Whether to match whole words only. Defaults to False.

        Returns:
            list: List of SearchResult objects
        """
        results = []
        success = False

        # First try the standard method
        try:
            lines = safe_read_text(file_path)
            standard_results, standard_success = cls._parse_text_lines(
                lines, file_path, file_name, keywords, case_sensitive, use_regex, whole_word
            )
            results.extend(standard_results)
            success = standard_success
        except Exception as e:
            logger.debug(f"Standard text reading failed for {file_name}: {e}")
            # Not logging as error since we'll try binary fallback

        # If standard method didn't work, try reading binary and searching
        if not success:
            logger.debug(f"Falling back to binary reading for {file_name}")
            try:
                with open(file_path, 'rb') as f:
                    binary_data = f.read()

                binary_results = cls._search_binary_content(
                    binary_data, file_path, file_name, keywords, case_sensitive, use_regex, whole_word
                )
                results.extend(binary_results)

            except Exception as e:
                log_file_error("read_error", file_path, e)

        return results
