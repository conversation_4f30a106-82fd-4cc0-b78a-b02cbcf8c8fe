# SearchTools Logging Guidelines

This document outlines the standard logging practices for the SearchTools project to ensure consistent and meaningful log messages across the codebase.

## Logging Levels

SearchTools uses the standard Python logging levels:

| Level | When to Use |
|-------|-------------|
| DEBUG | Detailed information, typically useful only for diagnosing problems |
| INFO | Confirmation that things are working as expected |
| WARNING | An indication that something unexpected happened, or may happen in the near future |
| ERROR | Due to a more serious problem, the software has not been able to perform a function |
| CRITICAL | A serious error, indicating that the program itself may be unable to continue running |

## Guidelines for Using Logging Levels

### DEBUG Level
- Detailed diagnostic information
- Parser selection decisions
- File encoding detection details
- Fallback attempts
- Detailed progress information
- Performance metrics

### INFO Level
- Application startup and shutdown
- Search operations started/completed
- Files being processed
- Matches found
- Configuration loaded
- Features enabled/disabled
- User actions (UI events)
- Archive extraction completed

### WARNING Level
- Missing optional dependencies
- Parser not available for a file
- File format issues that can be handled
- Performance degradation
- Fallbacks being used
- Potential issues that don't prevent operation

### ERROR Level
- File access failures
- Parser failures
- Archive extraction failures
- Network errors
- Configuration errors
- Any failure that prevents a specific operation

### CRITICAL Level
- Application initialization failures
- Database corruption
- Critical resource unavailability
- Any failure that prevents the application from continuing

## Standard Log Messages

To ensure consistency, use the helper functions in `searchtools.utils.logging_guidelines` for common logging scenarios:

```python
from searchtools.utils.logging_guidelines import log_parser_error

# Instead of:
logger.warning(f"PDF parsing failed for {file_name}: {e}")

# Use:
log_parser_error("PDF", file_name, e)
```

## Best Practices

1. **Be Specific**: Include relevant details in log messages (file names, error messages, etc.)
2. **Be Consistent**: Use the same format for similar events
3. **Be Concise**: Keep log messages clear and to the point
4. **Use the Right Level**: Follow the guidelines for each level
5. **Include Context**: Provide enough information to understand what happened
6. **Don't Log Sensitive Data**: Avoid logging passwords, personal information, etc.
7. **Use Helper Functions**: Use the provided logging helper functions for common scenarios

## Examples

### Good Examples

```python
# Good - Clear, specific, and at the right level
logger.error(f"Cannot access file: {file_path}")
logger.info(f"Found match for '{keyword}' in {file_name}")
logger.debug(f"Selected parser: {parser_name} for {file_name}")
```

### Bad Examples

```python
# Bad - Too vague
logger.error("File error")

# Bad - Wrong level (should be ERROR)
logger.warning("Failed to read required configuration file")

# Bad - Wrong level (should be WARNING or DEBUG)
logger.error("Using fallback parser")
```

## Implementation

The logging guidelines are implemented in the `searchtools.utils.logging_guidelines` module, which provides helper functions for common logging scenarios. Use these functions whenever possible to ensure consistent logging across the codebase.
