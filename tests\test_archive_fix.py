"""
Test script to verify the fix for the archive parser error
"""

import os
import sys
import zipfile
import tempfile
import logging
import shutil

# Configure logging
logging.basicConfig(level=logging.DEBUG,
                   format='%(asctime)s - %(levelname)s - %(message)s')

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

from searchtools.parsers.archive_parser import ArchiveParser

def test_archive_fix():
    """Test the fix for the archive parser error"""
    print("Testing archive parser fix...")

    # Create a test ZIP file
    temp_dir = tempfile.mkdtemp()
    zip_path = os.path.join(temp_dir, "test.zip")

    # Create a simple ZIP file with a text file inside
    with zipfile.ZipFile(zip_path, 'w') as zip_file:
        zip_file.writestr("test.txt", "This is a test file with some test content.")

    print(f"Created test ZIP file at {zip_path}")

    # Create a mock scanner callback
    def scanner_callback(extracted_dir):
        print(f"Scanner callback called with directory: {extracted_dir}")
        # List files in the extracted directory
        for root, _, files in os.walk(extracted_dir):
            for file in files:
                print(f"  - {file}")
        return True

    # Test the parse_zip method
    temp_dirs = []
    result = ArchiveParser.parse_zip(zip_path, "test.zip", scanner_callback, temp_dirs)

    print(f"parse_zip result: {result}")
    print(f"Temporary directories created: {temp_dirs}")

    # Use the imported shutil module

    # Clean up
    for temp_dir in temp_dirs:
        try:
            shutil.rmtree(temp_dir)
            print(f"Cleaned up temporary directory: {temp_dir}")
        except Exception as e:
            print(f"Error cleaning up {temp_dir}: {e}")

    # Clean up the test ZIP file
    try:
        shutil.rmtree(temp_dir)
        print(f"Cleaned up test directory: {temp_dir}")
    except Exception as e:
        print(f"Error cleaning up {temp_dir}: {e}")

    print("Test completed successfully!")

if __name__ == "__main__":
    test_archive_fix()
