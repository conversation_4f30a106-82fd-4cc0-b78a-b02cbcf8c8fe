"""
Parser for CSV files
"""

import pandas as pd
import os
from typing import List

from searchtools.utils.encoding import detect_encoding
from searchtools.core.searcher import TextSearcher
from searchtools.core.result import SearchResult
from searchtools.core.result_factory import SearchResultFactory
from searchtools.parsers.base_parser import BaseParser
from searchtools.utils.error_handling import (
    handle_parser_error, handle_file_operation, safe_operation,
    ErrorHandlingPolicy, ErrorAction, LogLevel
)
from searchtools.utils.logging_guidelines import (
    log_parser_fallback, log_file_error, log_search_match
)

@handle_file_operation
def safe_read_csv(file_path):
    """
    Read CSV files completely without size restrictions.

    Args:
        file_path (str): Path to the CSV file

    Returns:
        pandas.DataFrame: DataFrame containing the CSV data
    """
    # Create a policy for CSV reading that falls back to line-by-line reading
    csv_read_policy = ErrorHandlingPolicy(
        action=ErrorAction.LOG_AND_CONTINUE,
        log_level=LogLevel.WARNING,
        include_traceback=True
    )

    # Normal processing without size limitations
    encoding = detect_encoding(file_path)

    # Check pandas version for correct parameters
    pandas_version = pd.__version__.split('.')
    major = int(pandas_version[0])

    # Define the pandas read operation
    def read_with_pandas():
        # In pandas 2.0+, 'error_bad_lines' was renamed to 'on_bad_lines'
        if major >= 2:
            return pd.read_csv(
                file_path,
                dtype=str,
                low_memory=True,
                na_filter=False,
                encoding=encoding,
                on_bad_lines='skip'
            )
        else:
            # For older pandas versions, use error_bad_lines
            return pd.read_csv(
                file_path,
                dtype=str,
                low_memory=True,
                na_filter=False,
                encoding=encoding,
                error_bad_lines=False
            )

    # Try pandas read first
    result = safe_operation(
        read_with_pandas,
        f"CSV read error for {os.path.basename(file_path)}",
        policy=csv_read_policy,
        context={"file_path": file_path, "method": "pandas"}
    )

    if result is not None:
        return result

    # If pandas read fails, try line-by-line reading
    log_parser_fallback("line-by-line", os.path.basename(file_path))

    try:
        return read_csv_line_by_line(file_path)
    except Exception as e:
        log_file_error("read_error", file_path, e)
        # Return empty DataFrame as last resort
        return pd.DataFrame()

@handle_file_operation
def read_csv_line_by_line(file_path):
    """
    Read CSV manually line by line without size limits.

    Args:
        file_path (str): Path to the CSV file

    Returns:
        pandas.DataFrame: DataFrame containing the CSV data
    """
    encoding = detect_encoding(file_path)
    data = []
    headers = None

    with open(file_path, 'r', encoding=encoding, errors='replace') as f:
        for i, line in enumerate(f):
            if i == 0:
                # Assume first line is header
                headers = line.strip().split(',')
                continue

            # No line limit - process all lines
            values = line.strip().split(',')
            row_data = {}
            for j, val in enumerate(values):
                if j < len(headers):
                    header = headers[j]
                else:
                    header = f"Column_{j}"
                row_data[header] = val.strip('"\'')
            data.append(row_data)

    return pd.DataFrame(data)

class CSVParser(BaseParser):
    """
    Parser for CSV files.
    """

    @classmethod
    def get_name(cls) -> str:
        """
        Get the name of the parser.

        Returns:
            str: Parser name
        """
        return "CSV Parser"

    @classmethod
    def get_supported_extensions(cls) -> List[str]:
        """
        Get the list of file extensions supported by this parser.

        Returns:
            List[str]: List of supported file extensions
        """
        return ['.csv']

    @classmethod
    def get_priority(cls) -> int:
        """
        Get the priority of the parser.

        Returns:
            int: Priority value
        """
        return 10

    @classmethod
    def is_available(cls) -> bool:
        """
        Check if CSV parsing is available.

        Returns:
            bool: True if CSV parsing is available, False otherwise
        """
        # Create a policy for dependency checking
        dependency_policy = ErrorHandlingPolicy(
            action=ErrorAction.RETURN_DEFAULT,
            log_level=LogLevel.DEBUG,
            default_value=False,
            include_traceback=False
        )

        # Check if pandas is available
        return safe_operation(
            lambda: bool(pd),
            "Checking pandas availability",
            policy=dependency_policy,
            context={"dependency": "pandas"},
            exception_types=ImportError
        )

    @classmethod
    @handle_parser_error
    def parse(cls, file_path: str, file_name: str, keywords: List[str], case_sensitive: bool = False,
             use_regex: bool = False, whole_word: bool = False) -> List[SearchResult]:
        """
        Parse a CSV file and search for keywords.

        Args:
            file_path (str): Path to the file
            file_name (str): Name of the file
            keywords (list): List of keywords to search for
            case_sensitive (bool, optional): Whether to use case-sensitive search. Defaults to False.
            use_regex (bool, optional): Whether to treat keywords as regular expressions. Defaults to False.
            whole_word (bool, optional): Whether to match whole words only. Defaults to False.

        Returns:
            list: List of SearchResult objects
        """
        results = []
        df = safe_read_csv(file_path)
        if not df.empty:
            for col in df.columns:
                for idx, cell in enumerate(df[col]):
                    if cell is not None:
                        cell_str = str(cell).strip()
                        if cell_str:
                            found_keyword = TextSearcher.search_text(cell_str, keywords, case_sensitive, use_regex, whole_word)
                            if found_keyword:
                                log_search_match("CSV", file_name, f"Col: {col}, Row: {idx+1}", found_keyword)
                                result = SearchResultFactory.create_csv_result(
                                    file_name=file_name,
                                    file_path=file_path,
                                    keyword=found_keyword,
                                    column_name=col,
                                    row_number=idx+1,
                                    cell_content=cell_str
                                )
                                results.append(result)
        return results


