2025-06-20 18:22:23,296 - INFO - PyPDF2 configured. PDF parsing support enabled.
2025-06-20 18:22:23,379 - INFO - win32<PERSON> configured. DOC parsing support enabled.
2025-06-20 18:22:23,393 - DEBUG - antiword not available as fallback for DOC files.
2025-06-20 18:22:23,397 - WARNING - pyexcel-ods not available. ODS support disabled.
2025-06-20 18:22:23,397 - DEBUG - ezodf not available as alternative for ODS files.
2025-06-20 18:22:24,922 - INFO - Application started
2025-06-20 18:22:24,922 - INFO - Custom styling applied
2025-06-20 18:22:25,015 - INFO - rarfile configured. RAR parsing support enabled.
2025-06-20 18:22:25,475 - INFO - py7zr configured. 7z archive parsing support enabled.
2025-06-20 18:22:25,475 - DEBUG - Registered parser: Archive Parser
2025-06-20 18:22:25,475 - DEBUG - Registered parser: CSV Parser
2025-06-20 18:22:25,477 - DEBUG - Registered parser: DOC Parser
2025-06-20 18:22:25,477 - DEBUG - Registered parser: DOCX Parser
2025-06-20 18:22:25,477 - DEBUG - Registered parser: Excel Parser
2025-06-20 18:22:25,477 - DEBUG - Registered parser: ODS Parser
2025-06-20 18:22:25,477 - DEBUG - Registered parser: PDF Parser
2025-06-20 18:22:25,477 - DEBUG - Registered parser: Text Parser
2025-06-20 18:22:25,477 - INFO - Discovered 8 parser plugins
2025-06-20 18:22:25,495 - INFO - Looking for logo at (method 1): C:\Users\<USER>\Desktop\L457\SearchTools-v1\icon.ico
2025-06-20 18:22:25,495 - INFO - Logo file found at: C:\Users\<USER>\Desktop\L457\SearchTools-v1\icon.ico
2025-06-20 18:22:25,655 - INFO - Image opened, mode: RGBA, size: (159, 31)
2025-06-20 18:22:25,655 - INFO - Resized image to 100x19 to maintain aspect ratio
2025-06-20 18:22:25,685 - INFO - Converted to PhotoImage
2025-06-20 18:22:25,692 - INFO - Bottom logo loaded successfully
2025-06-20 18:22:25,910 - INFO - UI initialized
2025-06-20 18:24:09,780 - INFO - Window closed by user
2025-06-20 18:24:09,997 - INFO - Application exited
