"""
Test script for scanner logging improvements
"""

import os
import sys
from searchtools.core.scanner import Scanner

def test_scanner_logging():
    """Test the scanner logging improvements"""
    print("Testing scanner logging improvements...")
    
    # Create a scanner instance
    scanner = Scanner()
    
    # Set up a simple test directory
    test_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Start a scan
    scanner.start_scan(
        root_folder=test_dir,
        keywords=["test"],
        case_sensitive=False
    )
    
    # Wait for the scan to complete
    import time
    while scanner.scan_in_progress:
        time.sleep(0.1)
    
    # Print the results
    print(f"Found {len(scanner.get_results())} results")
    
    print("\nAll tests completed.")

if __name__ == "__main__":
    test_scanner_logging()
