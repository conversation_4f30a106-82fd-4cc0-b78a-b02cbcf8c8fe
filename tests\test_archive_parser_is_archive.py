"""
Test script for ArchiveParser.is_archive refactoring
"""

from searchtools.parsers.archive_parser import Archive<PERSON>ars<PERSON>

def test_is_archive():
    """Test the is_archive method"""
    print("Testing ArchiveParser.is_archive method...")
    
    # Test with various file paths
    test_files = [
        "test.zip",                  # Standard ZIP file
        "test.rar",                  # RAR file
        "test.7z",                   # 7z file with extension
        "test.7z.001",               # Split 7z file
        "test_without_extension",    # No extension
        "test.txt",                  # Text file
        "archive.7z.txt",            # Text file with 7z in name
        "archive.zip.rar",           # RAR file with zip in name
        "ARCHIVE.ZIP",               # Uppercase extension
        "archive.ZIP",               # Mixed case extension
        "test.tar.gz",               # Tar.gz file (not supported)
    ]
    
    # Get supported extensions for reference
    supported_extensions = ArchiveParser.get_supported_extensions()
    print(f"Supported extensions: {supported_extensions}")
    
    # Test each file
    for file_path in test_files:
        is_archive = ArchiveParser.is_archive(file_path)
        print(f"Is '{file_path}' an archive: {is_archive}")
    
    print("\nAll tests completed.")

if __name__ == "__main__":
    test_is_archive()
