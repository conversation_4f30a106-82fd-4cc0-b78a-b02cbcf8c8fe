"""
Parser for DOCX files
"""

from typing import List

from searchtools.utils.logging import logger
from searchtools.core.searcher import TextSearcher
from searchtools.core.result import SearchResult
from searchtools.parsers.base_parser import BaseParser

# Check if docx is available
DOCX_AVAILABLE = False
try:
    import docx
    DOCX_AVAILABLE = True
except ImportError:
    logger.warning("python-docx not available. DOCX support disabled.")

class DocxParser(BaseParser):
    """
    Parser for DOCX files.
    """

    @classmethod
    def get_name(cls) -> str:
        """
        Get the name of the parser.

        Returns:
            str: Parser name
        """
        return "DOCX Parser"

    @classmethod
    def get_supported_extensions(cls) -> List[str]:
        """
        Get the list of file extensions supported by this parser.

        Returns:
            List[str]: List of supported file extensions
        """
        return ['.docx']

    @classmethod
    def get_priority(cls) -> int:
        """
        Get the priority of the parser.

        Returns:
            int: Priority value
        """
        return 10

    @classmethod
    def is_available(cls) -> bool:
        """
        Check if DOCX parsing is available.

        Returns:
            bool: True if DOCX parsing is available, False otherwise
        """
        return DOCX_AVAILABLE

    @classmethod
    def parse(cls, file_path: str, file_name: str, keywords: List[str], case_sensitive: bool = False,
             use_regex: bool = False, whole_word: bool = False) -> List[SearchResult]:
        """
        Parse a DOCX file and search for keywords.

        Args:
            file_path (str): Path to the file
            file_name (str): Name of the file
            keywords (list): List of keywords to search for
            case_sensitive (bool, optional): Whether to use case-sensitive search. Defaults to False.
            use_regex (bool, optional): Whether to treat keywords as regular expressions. Defaults to False.
            whole_word (bool, optional): Whether to match whole words only. Defaults to False.

        Returns:
            list: List of SearchResult objects
        """
        if not DOCX_AVAILABLE:
            logger.warning(f"DOCX parsing not available for {file_name}")
            return []

        results = []
        try:
            doc = docx.Document(file_path)
            for para_idx, para in enumerate(doc.paragraphs):
                if para.text:
                    found_keyword = TextSearcher.search_text(para.text, keywords, case_sensitive, use_regex, whole_word)
                    if found_keyword:
                        logger.info(f"Match in DOCX {file_name} [Para: {para_idx+1}]: '{found_keyword}'")
                        results.append(
                            SearchResult(
                                file=file_name,
                                path=file_path,
                                keyword=found_keyword,
                                context=para.text.strip(),
                                location=f"DOCX: Paragraph {para_idx+1}",
                                full_context=para.text.strip()
                            )
                        )
        except Exception as e:
            logger.warning(f"DOCX parsing failed for {file_name}: {e}")

        return results
