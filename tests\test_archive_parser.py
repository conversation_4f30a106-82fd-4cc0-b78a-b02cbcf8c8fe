"""
Test script for ArchiveParser refactoring
"""

from searchtools.parsers.archive_parser import Archive<PERSON>arser

def test_archive_parser():
    """Test the ArchiveParser class"""
    # Test initialization
    print("Testing ArchiveParser initialization...")
    ArchiveParser._initialize_capabilities()
    
    # Test supported extensions
    extensions = ArchiveParser.get_supported_extensions()
    print(f"Supported extensions: {extensions}")
    
    # Test availability
    print(f"Is archive parsing available: {ArchiveParser.is_available()}")
    print(f"Is RAR parsing available: {ArchiveParser.is_rar_available()}")
    print(f"Is 7z parsing available: {ArchiveParser.is_7z_available()}")
    
    # Test is_archive method
    test_files = [
        "test.zip",
        "test.rar",
        "test.7z",
        "test.txt"
    ]
    
    for file in test_files:
        is_archive = ArchiveParser.is_archive(file)
        print(f"Is {file} an archive: {is_archive}")

if __name__ == "__main__":
    test_archive_parser()
