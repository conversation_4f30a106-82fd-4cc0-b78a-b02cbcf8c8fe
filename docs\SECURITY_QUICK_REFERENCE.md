# Security Quick Reference Guide

## 🚀 Quick Start

### Enable Security Features

Security features are enabled by default. To customize:

```python
# config.py
ARCHIVE_SECURITY = {
    "max_extracted_size": 1024 * 1024 * 1024,  # 1GB
    "max_files_per_archive": 10000,
    "strict_path_validation": True
}
```

### Secure Archive Processing

```python
from searchtools.parsers.archive_parser import ArchiveParser

# Archives are now processed securely by default
result = ArchiveParser.parse_zip(file_path, file_name, callback, temp_dirs)
```

### Secure Temporary Directories

```python
from searchtools.utils.temp_manager import temp_manager

# Recommended: Use context manager
with temp_manager.temp_directory() as temp_dir:
    # Your code here
    pass  # Automatic cleanup

# Alternative: Manual management
temp_dir = temp_manager.create_temp_dir()
try:
    # Your code here
finally:
    temp_manager.cleanup_temp_dir(temp_dir)
```

## 🛡️ Security Checks

### Path Traversal Protection

```python
from searchtools.utils.security import SecurePathValidator

validator = SecurePathValidator()
try:
    safe_path = validator.validate_path("user/input/path", "/base/dir")
    # Path is safe to use
except PathTraversalError:
    # Handle malicious path
    pass
```

### Resource Limit Validation

```python
from searchtools.utils.security import ArchiveSecurityManager

security_manager = ArchiveSecurityManager()
try:
    safe_path = security_manager.validate_extraction(
        member_name="file.txt",
        member_size=1024000,
        base_dir="/tmp"
    )
    # Safe to extract
except ResourceExhaustionError:
    # Resource limit exceeded
    pass
```

## ⚙️ Configuration Options

### Archive Security Settings

| Setting | Default | Description |
|---------|---------|-------------|
| `max_extracted_size` | 1GB | Maximum total extracted size |
| `max_files_per_archive` | 10,000 | Maximum files per archive |
| `max_individual_file_size` | 500MB | Maximum individual file size |
| `max_path_length` | 260 | Maximum file path length |
| `max_directory_depth` | 20 | Maximum directory nesting |
| `blocked_extensions` | ['.exe', '.bat', ...] | Blocked file extensions |
| `strict_path_validation` | True | Enable strict path validation |

### Temp Directory Security Settings

| Setting | Default | Description |
|---------|---------|-------------|
| `max_temp_dirs` | 100 | Maximum concurrent temp dirs |
| `max_temp_dir_age` | 3600s | Maximum age before cleanup |
| `cleanup_on_exit` | True | Cleanup on application exit |
| `periodic_cleanup` | True | Enable periodic cleanup |
| `cleanup_interval` | 300s | Cleanup interval |
| `secure_deletion` | False | Overwrite files before deletion |

## 🚨 Error Handling

### Security Exceptions

```python
from searchtools.utils.security import (
    SecurityError, PathTraversalError, ResourceExhaustionError
)

try:
    # Security-sensitive operation
    pass
except PathTraversalError as e:
    logger.warning(f"Path traversal attempt: {e}")
except ResourceExhaustionError as e:
    logger.error(f"Resource limit exceeded: {e}")
except SecurityError as e:
    logger.error(f"Security violation: {e}")
```

### Logging Security Events

```python
import logging
from searchtools.utils.logging import logger

# Security events are automatically logged
# Check logs for messages like:
# WARNING: Security violation extracting ../../../etc/passwd
# ERROR: Resource limit exceeded: File too large
# DEBUG: Safely extracted: document.txt
```

## 🧪 Testing Security

### Run Security Tests

```bash
# All security tests
python -m pytest tests/test_security.py -v

# Specific test categories
python -m pytest tests/test_security.py::TestSecurePathValidator -v
python -m pytest tests/test_security.py::TestMaliciousArchiveHandling -v
```

### Create Test Archives

```python
import zipfile

# Create test archive with malicious content
with zipfile.ZipFile("test_malicious.zip", 'w') as zf:
    zf.writestr("safe_file.txt", "safe content")
    zf.writestr("../../../malicious.txt", "malicious content")

# Test with your security implementation
```

## 📊 Monitoring

### Get Security Statistics

```python
from searchtools.utils.security import ArchiveSecurityManager
from searchtools.utils.temp_manager import temp_manager

# Archive extraction stats
security_manager = ArchiveSecurityManager()
stats = security_manager.get_extraction_stats()
print(f"Files extracted: {stats['files_extracted']}")
print(f"Total size: {stats['total_size']} bytes")

# Temp directory stats
temp_stats = temp_manager.get_stats()
print(f"Active temp dirs: {temp_stats['count']}")
print(f"Total size: {temp_stats['total_size']} bytes")
```

### Security Metrics

Monitor these key metrics:
- Number of security violations per hour
- Resource limit violations
- Failed archive extractions
- Temp directory cleanup failures

## 🔧 Troubleshooting

### Common Issues

**Archive extraction fails with SecurityError**
```python
# Check security logs
logger.info("Security violation detected")
# Adjust limits in config.py if needed
ARCHIVE_SECURITY["max_extracted_size"] = 2 * 1024 * 1024 * 1024  # 2GB
```

**Too many temp directories error**
```python
# Increase limit or force cleanup
TEMP_DIR_SECURITY["max_temp_dirs"] = 200
# Or manually cleanup
temp_manager.cleanup_all()
```

**Performance degradation**
```python
# Disable strict validation for development
ARCHIVE_SECURITY["strict_path_validation"] = False
# Or increase resource limits
ARCHIVE_SECURITY["max_files_per_archive"] = 50000
```

### Debug Mode

```python
import logging
logging.getLogger('searchtools').setLevel(logging.DEBUG)

# Enable detailed security logging
logger.debug("Security validation details will be logged")
```

## 🔄 Migration Checklist

- [ ] Update configuration with security settings
- [ ] Replace manual temp directory management with temp_manager
- [ ] Add security exception handling to archive processing
- [ ] Run security tests to verify functionality
- [ ] Monitor security logs for violations
- [ ] Adjust resource limits based on usage patterns

## 📞 Emergency Procedures

### Disable Security (Emergency Only)

```python
# ONLY for emergency situations
ARCHIVE_SECURITY["strict_path_validation"] = False
ARCHIVE_SECURITY["max_extracted_size"] = float('inf')
ARCHIVE_SECURITY["max_files_per_archive"] = float('inf')

# Remember to re-enable security after resolving the issue
```

### Force Cleanup All Resources

```python
from searchtools.utils.temp_manager import temp_manager

# Emergency cleanup
try:
    cleaned = temp_manager.cleanup_all()
    logger.info(f"Emergency cleanup: {cleaned} directories removed")
except Exception as e:
    logger.error(f"Emergency cleanup failed: {e}")
```

---

**Quick Reference Version**: 1.0  
**Last Updated**: Phase 1 Implementation  
**Compatibility**: SearchTools-v1 with security enhancements
