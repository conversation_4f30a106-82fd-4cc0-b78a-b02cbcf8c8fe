"""
Logging utilities for the SearchTools application
"""

import os
import logging
from logging.handlers import RotatingFileHandler
from datetime import datetime
import sys
import config

def setup_logging():
    """
    Set up logging for the application.
    Returns a configured logger instance.
    """
    # Create logs directory if it doesn't exist
    if not os.path.exists(config.LOG_DIR):
        os.makedirs(config.LOG_DIR)

    # Create a logger
    logger = logging.getLogger('KeywordScanner')
    logger.setLevel(logging.DEBUG)

    # Create handlers
    log_file = os.path.join(config.LOG_DIR, f'scan_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    # Explicitly set encoding to UTF-8 for the file handler
    file_handler = RotatingFileHandler(
        log_file,
        maxBytes=config.LOG_MAX_SIZE,
        backupCount=config.LOG_BACKUP_COUNT,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)

    # Create formatters and add it to handlers
    log_format = logging.Formatter(config.LOG_FORMAT)
    file_handler.setFormatter(log_format)

    # Add handlers to the logger
    logger.addHandler(file_handler)

    # Add a StreamHandler for console output, also using UTF-8
    # This helps if the console itself has issues with default encoding
    stream_handler = logging.StreamHandler()
    stream_handler.setLevel(logging.INFO)  # Only show INFO level and above on console
    stream_handler.setFormatter(log_format)
    stream_handler.setStream(open(os.devnull, 'w'))  # Initially point to null
    try:
        # Attempt to use sys.stderr with utf-8 encoding
        stream_handler.setStream(open(sys.stderr.fileno(), mode='w', encoding='utf-8', errors='replace'))
    except Exception:
        # Fallback if stderr redirection fails (e.g., in some environments)
        pass  # Keep pointing to devnull or consider basic print
    logger.addHandler(stream_handler)

    return logger

class TextHandler(logging.Handler):
    """Custom handler for GUI logging"""
    def __init__(self, text_widget):
        logging.Handler.__init__(self)
        # Explicitly set encoding for the GUI handler's formatter too
        self.setFormatter(logging.Formatter(config.LOG_FORMAT))
        self.text_widget = text_widget

    def emit(self, record):
        try:
            # Check if the text widget still exists and is valid
            if not self.text_widget.winfo_exists():
                return

            msg = self.format(record) + '\n'
            # Add color tags based on level
            if record.levelno >= logging.ERROR:
                self.text_widget.insert("end", msg, "error")
            elif record.levelno >= logging.WARNING:
                self.text_widget.insert("end", msg, "warning")
            elif record.levelno >= logging.INFO:
                self.text_widget.insert("end", msg, "info")
            else:
                self.text_widget.insert("end", msg)
            self.text_widget.see("end")
            self.text_widget.update_idletasks()
        except Exception as e:
            # Basic print as fallback if GUI logging fails
            # Only print if it's not a common error during shutdown
            if "invalid command name" not in str(e):
                print(f"GUI Logging Error: {e}")

# Create a global logger instance
logger = setup_logging()
