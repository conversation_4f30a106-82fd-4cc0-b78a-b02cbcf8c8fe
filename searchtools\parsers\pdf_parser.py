"""
Parser for PDF files
"""

from typing import List

from searchtools.utils.logging_guidelines import (
    log_parser_not_available, log_parser_error, log_search_match,
    log_dependency_status
)
from searchtools.core.searcher import TextSearcher
from searchtools.core.result import SearchResult
from searchtools.parsers.base_parser import BaseParser

# Check if PyPDF2 is available
PDF_AVAILABLE = False
try:
    import PyPDF2
    PDF_AVAILABLE = True
    log_dependency_status("configured", "PyPDF2", "PDF parsing")
except ImportError:
    log_dependency_status("missing", "PyPDF2", "PDF parsing")

class PDFParser(BaseParser):
    """
    Parser for PDF files.
    """

    @classmethod
    def get_name(cls) -> str:
        """
        Get the name of the parser.

        Returns:
            str: Parser name
        """
        return "PDF Parser"

    @classmethod
    def get_supported_extensions(cls) -> List[str]:
        """
        Get the list of file extensions supported by this parser.

        Returns:
            List[str]: List of supported file extensions
        """
        return ['.pdf']

    @classmethod
    def get_priority(cls) -> int:
        """
        Get the priority of the parser.

        Returns:
            int: Priority value
        """
        return 10

    @classmethod
    def is_available(cls) -> bool:
        """
        Check if PDF parsing is available.

        Returns:
            bool: True if PDF parsing is available, False otherwise
        """
        return PDF_AVAILABLE

    @classmethod
    def parse(cls, file_path: str, file_name: str, keywords: List[str], case_sensitive: bool = False,
             use_regex: bool = False, whole_word: bool = False) -> List[SearchResult]:
        """
        Parse a PDF file and search for keywords.

        Args:
            file_path (str): Path to the file
            file_name (str): Name of the file
            keywords (list): List of keywords to search for
            case_sensitive (bool, optional): Whether to use case-sensitive search. Defaults to False.
            use_regex (bool, optional): Whether to treat keywords as regular expressions. Defaults to False.
            whole_word (bool, optional): Whether to match whole words only. Defaults to False.

        Returns:
            list: List of SearchResult objects
        """
        if not PDF_AVAILABLE:
            log_parser_not_available("PDF", file_name)
            return []

        results = []
        try:
            with open(file_path, 'rb') as f:
                pdf = PyPDF2.PdfReader(f)
                for page_num in range(len(pdf.pages)):
                    page = pdf.pages[page_num]
                    text = page.extract_text()
                    if text:
                        found_keyword = TextSearcher.search_text(text, keywords, case_sensitive, use_regex, whole_word)
                        if found_keyword:
                            context = text[:200].strip().replace('\n', ' ')
                            location = f"Page: {page_num+1}"
                            log_search_match("PDF", file_name, location, found_keyword)
                            results.append(
                                SearchResult(
                                    file=file_name,
                                    path=file_path,
                                    keyword=found_keyword,
                                    context=context,
                                    location=f"PDF: Page {page_num+1}",
                                    full_context=context
                                )
                            )
        except Exception as e:
            log_parser_error("PDF", file_name, e)

        return results
