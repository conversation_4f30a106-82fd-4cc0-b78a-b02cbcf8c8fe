"""
Test script for the new ErrorHandlingPolicy implementation
"""

import os
import sys
from searchtools.utils.error_handling import (
    ErrorHandlingPolicy, ErrorAction, LogLevel,
    PARSER_POLICY, FILE_OPERATION_POLICY, ARCHIVE_OPERATION_POLICY, SEARCH_OPERATION_POLICY,
    handle_parser_error, handle_file_operation, safe_operation, handle_archive_operation,
    FileReadError, EncodingError, ArchiveFormatError
)

def test_error_handling_policy():
    """Test the ErrorHandlingPolicy implementation"""
    print("Testing ErrorHandlingPolicy implementation...")

    # Test different policies
    print("\nTesting different policies...")

    # Create a function that always raises an exception
    def failing_function():
        raise ValueError("Test error")

    # Test with RETURN_DEFAULT policy
    print("\nTesting RETURN_DEFAULT policy...")
    policy1 = ErrorHandlingPolicy(
        action=ErrorAction.RETURN_DEFAULT,
        log_level=LogLevel.ERROR,
        default_value="Default Value"
    )

    try:
        result = policy1.handle_error(ValueError("Test error"), {"context": "test"}, "Test error")
        print(f"Result with RETURN_DEFAULT policy: {result}")
    except Exception as e:
        print(f"Unexpected exception: {e}")

    # Test with RAISE_CUSTOM policy
    print("\nTesting RAISE_CUSTOM policy...")
    policy2 = ErrorHandlingPolicy(
        action=ErrorAction.RAISE_CUSTOM,
        log_level=LogLevel.ERROR,
        custom_exception_type=FileReadError
    )

    try:
        result = policy2.handle_error(ValueError("Test error"), {"context": "test"}, "Test error")
        print(f"Result with RAISE_CUSTOM policy: {result}")
    except FileReadError as e:
        print(f"Correctly raised FileReadError: {e}")
    except Exception as e:
        print(f"Unexpected exception: {e}")

    # Test with LOG_AND_CONTINUE policy
    print("\nTesting LOG_AND_CONTINUE policy...")
    policy3 = ErrorHandlingPolicy(
        action=ErrorAction.LOG_AND_CONTINUE,
        log_level=LogLevel.WARNING
    )

    try:
        result = policy3.handle_error(ValueError("Test error"), {"context": "test"}, "Test error")
        print(f"Result with LOG_AND_CONTINUE policy: {result}")
    except Exception as e:
        print(f"Unexpected exception: {e}")

    # Test handle_parser_error decorator
    print("\nTesting handle_parser_error decorator...")

    @handle_parser_error
    def parse_test_file(file_path, file_name):
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
        return ["Result 1", "Result 2"]

    # Test with existing file
    result = parse_test_file(__file__, os.path.basename(__file__))
    print(f"Result with existing file: {result}")

    # Test with non-existing file
    result = parse_test_file("non_existing_file.txt", "non_existing_file.txt")
    print(f"Result with non-existing file: {result}")

    # Test handle_file_operation decorator
    print("\nTesting handle_file_operation decorator...")

    # Create a policy that returns a default value
    file_policy = ErrorHandlingPolicy(
        action=ErrorAction.RETURN_DEFAULT,
        log_level=LogLevel.ERROR,
        default_value="Default Value"
    )

    @handle_file_operation(policy=file_policy)
    def read_file(file_path):
        with open(file_path, 'r') as f:
            return f.read()

    # Test with existing file
    result = read_file(__file__)
    print(f"Result with existing file: {result[:50]}...")

    # Test with non-existing file
    result = read_file("non_existing_file.txt")
    print(f"Result with non-existing file: {result}")

    # Create a policy that raises exceptions
    raise_policy = ErrorHandlingPolicy(
        action=ErrorAction.RAISE_CUSTOM,
        log_level=LogLevel.ERROR,
        custom_exception_type=FileReadError
    )

    @handle_file_operation(policy=raise_policy)
    def read_file_raise(file_path):
        with open(file_path, 'r') as f:
            return f.read()

    # Test with non-existing file that should raise an exception
    try:
        result = read_file_raise("another_non_existing_file.txt")
        print(f"Result with non-existing file (should not reach here): {result}")
    except FileReadError as e:
        print(f"Correctly raised FileReadError: {e}")

    # Test safe_operation function
    print("\nTesting safe_operation function...")

    # Create a custom policy for testing
    custom_policy = ErrorHandlingPolicy(
        action=ErrorAction.RETURN_DEFAULT,
        log_level=LogLevel.WARNING,
        default_value="Custom Default"
    )

    # Test with a function that succeeds
    result = safe_operation(
        lambda: "Success",
        "Operation failed",
        policy=custom_policy
    )
    print(f"Result with successful operation: {result}")

    # Test with a function that fails
    result = safe_operation(
        failing_function,
        "Operation failed",
        policy=custom_policy,
        context={"test": "context"}
    )
    print(f"Result with failed operation: {result}")

    # Test handle_archive_operation function
    print("\nTesting handle_archive_operation function...")

    # Create a custom policy for testing
    archive_policy = ErrorHandlingPolicy(
        action=ErrorAction.RETURN_DEFAULT,
        log_level=LogLevel.ERROR,
        default_value=None
    )

    # Test with a function that succeeds
    result = handle_archive_operation(
        lambda: "Success",
        "ZIP",
        "test.zip",
        policy=archive_policy
    )
    print(f"Result with successful operation: {result}")

    # Test with a function that fails with ImportError
    result = handle_archive_operation(
        lambda: __import__("non_existing_module"),
        "RAR",
        "test.rar",
        policy=archive_policy
    )
    print(f"Result with ImportError: {result}")

    # Test with a function that fails with ValueError
    result = handle_archive_operation(
        lambda: int("not_a_number"),
        "7z",
        "test.7z",
        policy=archive_policy
    )
    print(f"Result with ValueError: {result}")

    print("\nAll tests completed.")

if __name__ == "__main__":
    test_error_handling_policy()
