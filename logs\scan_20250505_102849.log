2025-05-05 10:28:50,600 - INFO - PyPDF2 configured. PDF parsing support enabled.
2025-05-05 10:28:50,680 - INFO - win32com configured. DOC parsing support enabled.
2025-05-05 10:28:50,684 - DEBUG - antiword not available as fallback for DOC files.
2025-05-05 10:28:51,060 - INFO - Application started
2025-05-05 10:28:51,063 - INFO - Custom styling applied
2025-05-05 10:28:51,079 - INFO - rarfile configured. RAR parsing support enabled.
2025-05-05 10:28:51,489 - INFO - py7zr configured. 7z archive parsing support enabled.
2025-05-05 10:28:51,505 - DEBUG - Registered parser: Archive Parser
2025-05-05 10:28:51,508 - DEBUG - Registered parser: CSV Parser
2025-05-05 10:28:51,510 - DEBUG - Registered parser: DOC Parser
2025-05-05 10:28:51,511 - DEBUG - Registered parser: DOCX Parser
2025-05-05 10:28:51,513 - DEBUG - Registered parser: Excel Parser
2025-05-05 10:28:51,514 - DEBUG - Registered parser: ODS Parser
2025-05-05 10:28:51,514 - DEBUG - Registered parser: PDF Parser
2025-05-05 10:28:51,515 - DEBUG - Registered parser: Text Parser
2025-05-05 10:28:51,516 - INFO - Discovered 8 parser plugins
2025-05-05 10:28:51,992 - INFO - Looking for logo at (method 1): C:\Users\<USER>\Desktop\L457\SearchTools-v1\icon.ico
2025-05-05 10:28:51,993 - INFO - Logo file found at: C:\Users\<USER>\Desktop\L457\SearchTools-v1\icon.ico
2025-05-05 10:28:52,178 - INFO - Image opened, mode: RGBA, size: (159, 31)
2025-05-05 10:28:52,182 - INFO - Resized image to 100x19 to maintain aspect ratio
2025-05-05 10:28:52,199 - INFO - Converted to PhotoImage
2025-05-05 10:28:52,284 - INFO - Bottom logo loaded successfully
2025-05-05 10:28:55,383 - INFO - UI initialized
