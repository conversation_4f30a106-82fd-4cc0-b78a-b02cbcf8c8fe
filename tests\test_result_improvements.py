"""
Test script for SearchResult improvements
"""

import time
import sys
from searchtools.core.result import SearchResult
import config

def test_search_result_improvements():
    """Test the improved SearchResult class"""
    print("Testing improved SearchResult class...")
    
    # Test initialization with valid parameters
    try:
        result = SearchResult(
            file="test.txt",
            path="/path/to/test.txt",
            keyword="hello",
            context="This is a test file that contains the keyword hello in it.",
            location="Line 5",
            full_context="Full context: This is a test file that contains the keyword hello in it."
        )
        print("✓ Valid initialization successful")
    except Exception as e:
        print(f"✗ Valid initialization failed: {e}")
        
    # Test initialization with invalid parameters
    try:
        result = SearchResult(
            file="",  # Empty string should raise ValueError
            path="/path/to/test.txt",
            keyword="hello",
            context="This is a test file that contains the keyword hello in it."
        )
        print("✗ Empty file parameter should have raised ValueError")
    except ValueError as e:
        print(f"✓ Correctly caught ValueError: {e}")
    
    # Test initialization with None parameters
    try:
        result = SearchResult(
            file="test.txt",
            path="/path/to/test.txt",
            keyword=None,  # None should raise ValueError
            context="This is a test file that contains the keyword hello in it."
        )
        print("✗ None keyword parameter should have raised ValueError")
    except ValueError as e:
        print(f"✓ Correctly caught ValueError: {e}")
    
    # Create a valid result for further tests
    result = SearchResult(
        file="test.txt",
        path="/path/to/test.txt",
        keyword="hello",
        context="This is a test file that contains the keyword hello in it.",
        location="Line 5"
    )
    
    # Test get_display_context with default max_length
    display_context = result.get_display_context()
    print(f"Display context (default): {display_context}")
    
    # Test get_display_context with custom max_length
    display_context_short = result.get_display_context(max_length=20)
    print(f"Display context (short): {display_context_short}")
    
    # Test get_display_context with invalid max_length
    display_context_invalid = result.get_display_context(max_length=-10)
    print(f"Display context (invalid max_length): {display_context_invalid}")
    
    # Test get_display_context with no location
    result_no_location = SearchResult(
        file="test.txt",
        path="/path/to/test.txt",
        keyword="hello",
        context="This is a test file that contains the keyword hello in it."
    )
    display_context_no_location = result_no_location.get_display_context()
    print(f"Display context (no location): {display_context_no_location}")
    
    # Test performance of get_display_context
    print("\nPerformance test:")
    
    # Create a long context string
    long_context = "This is a very long context string. " * 100
    
    # Create a result with long context
    result_long = SearchResult(
        file="test.txt",
        path="/path/to/test.txt",
        keyword="hello",
        context=long_context,
        location="Line 5"
    )
    
    # Measure performance of get_display_context
    iterations = 10000
    start_time = time.time()
    for _ in range(iterations):
        result_long.get_display_context()
    end_time = time.time()
    
    print(f"Time to execute get_display_context {iterations} times: {end_time - start_time:.4f} seconds")
    
    print("\nAll tests completed.")

if __name__ == "__main__":
    test_search_result_improvements()
