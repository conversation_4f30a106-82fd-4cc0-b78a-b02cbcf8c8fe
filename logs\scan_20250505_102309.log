2025-05-05 10:23:15,308 - INFO - PyPDF2 configured. PDF parsing support enabled.
2025-05-05 10:23:15,978 - INFO - win32com configured. DOC parsing support enabled.
2025-05-05 10:23:15,986 - DEBUG - antiword not available as fallback for DOC files.
2025-05-05 10:23:17,812 - INFO - Application started
2025-05-05 10:23:17,816 - INFO - Custom styling applied
2025-05-05 10:23:17,827 - INFO - rarfile configured. RAR parsing support enabled.
2025-05-05 10:23:19,337 - INFO - py7zr configured. 7z archive parsing support enabled.
2025-05-05 10:23:19,339 - DEBUG - Registered parser: Archive Parser
2025-05-05 10:23:19,341 - DEBUG - Registered parser: CSV Parser
2025-05-05 10:23:19,342 - DEBUG - Registered parser: DOC Parser
2025-05-05 10:23:19,342 - DEBUG - Registered parser: DOCX Parser
2025-05-05 10:23:19,344 - DEBUG - Registered parser: Excel Parser
2025-05-05 10:23:19,344 - DEBUG - Registered parser: ODS Parser
2025-05-05 10:23:19,344 - DEBUG - Registered parser: PDF Parser
2025-05-05 10:23:19,345 - DEBUG - Registered parser: Text Parser
2025-05-05 10:23:19,345 - INFO - Discovered 8 parser plugins
2025-05-05 10:23:19,448 - INFO - Looking for logo at (method 1): c:\Users\<USER>\Desktop\L457\SearchTools-v1\icon.ico
2025-05-05 10:23:19,448 - INFO - Logo file found at: c:\Users\<USER>\Desktop\L457\SearchTools-v1\icon.ico
2025-05-05 10:23:19,776 - INFO - Image opened, mode: RGBA, size: (159, 31)
2025-05-05 10:23:19,779 - INFO - Resized image to 100x19 to maintain aspect ratio
2025-05-05 10:23:19,963 - INFO - Converted to PhotoImage
2025-05-05 10:23:19,976 - INFO - Bottom logo loaded successfully
2025-05-05 10:23:20,525 - INFO - UI initialized
2025-05-05 10:24:32,597 - INFO - Window closed by user
2025-05-05 10:24:32,882 - INFO - Application exited
