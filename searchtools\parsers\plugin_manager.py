"""
Plugin manager for file parsers
"""

import os
import importlib
import pkgutil
import inspect
from typing import Dict, List, Type, Optional

from searchtools.utils.logging import logger
from searchtools.parsers.base_parser import BaseParser

class ParserPluginManager:
    """
    Manager for parser plugins.

    This class handles the registration, discovery, and retrieval of parser plugins.
    """

    def __init__(self):
        """
        Initialize the plugin manager.
        """
        self._parsers: Dict[str, Type[BaseParser]] = {}
        self._extension_map: Dict[str, List[Type[BaseParser]]] = {}
        self._initialized = False

    def register_parser(self, parser_class: Type[BaseParser]) -> None:
        """
        Register a parser class.

        Args:
            parser_class (Type[BaseParser]): Parser class to register
        """
        if not issubclass(parser_class, BaseParser):
            logger.warning(f"Attempted to register non-parser class: {parser_class.__name__}")
            return

        parser_name = parser_class.get_name()

        # Register the parser by name
        self._parsers[parser_name] = parser_class

        # Register the parser for each supported extension
        for ext in parser_class.get_supported_extensions():
            ext = ext.lower()
            if ext not in self._extension_map:
                self._extension_map[ext] = []
            self._extension_map[ext].append(parser_class)

        logger.debug(f"Registered parser: {parser_name}")

    def discover_parsers(self) -> None:
        """
        Discover and register all available parsers.
        """
        if self._initialized:
            return

        # Import all modules in the parsers package
        import searchtools.parsers as parsers_pkg

        # Get the package path
        pkg_path = os.path.dirname(parsers_pkg.__file__)

        # Iterate through all modules in the package
        for _, module_name, _ in pkgutil.iter_modules([pkg_path]):
            # Skip the base module and this module
            if module_name in ['base_parser', 'plugin_manager', '__init__', '__pycache__']:
                continue

            try:
                # Import the module
                module = importlib.import_module(f"searchtools.parsers.{module_name}")

                # Find all parser classes in the module
                for name, obj in inspect.getmembers(module):
                    if (inspect.isclass(obj) and
                        issubclass(obj, BaseParser) and
                        obj != BaseParser):
                        self.register_parser(obj)
            except Exception as e:
                logger.error(f"Error loading parser module {module_name}: {e}")

        # Sort parsers by priority for each extension
        for ext in self._extension_map:
            self._extension_map[ext].sort(key=lambda p: p.get_priority(), reverse=True)

        self._initialized = True
        logger.info(f"Discovered {len(self._parsers)} parser plugins")

    def get_parser_for_file(self, file_path: str) -> Optional[Type[BaseParser]]:
        """
        Get the appropriate parser for a file.

        Args:
            file_path (str): Path to the file

        Returns:
            Optional[Type[BaseParser]]: Parser class or None if no suitable parser is found
        """
        if not self._initialized:
            self.discover_parsers()

        from searchtools.utils.file_utils import get_file_extension
        ext = get_file_extension(file_path).lower()

        # Check if we have parsers for this extension
        if ext in self._extension_map:
            # Try each parser in priority order
            for parser_class in self._extension_map[ext]:
                if parser_class.is_available() and parser_class.can_parse(file_path):
                    return parser_class

        # No suitable parser found
        return None

    def get_all_parsers(self) -> List[Type[BaseParser]]:
        """
        Get all registered parsers.

        Returns:
            List[Type[BaseParser]]: List of all parser classes
        """
        if not self._initialized:
            self.discover_parsers()

        return list(self._parsers.values())

    def get_supported_extensions(self) -> List[str]:
        """
        Get all supported file extensions.

        Returns:
            List[str]: List of all supported file extensions
        """
        if not self._initialized:
            self.discover_parsers()

        return list(self._extension_map.keys())



# Create a singleton instance
plugin_manager = ParserPluginManager()
