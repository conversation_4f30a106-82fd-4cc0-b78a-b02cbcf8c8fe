"""
Test script for error handling improvements
"""

import os
import tempfile
from searchtools.utils.file_utils import safe_read_text, create_temp_dir, cleanup_temp_dir
from searchtools.utils.encoding import detect_encoding, read_binary_fallback
from searchtools.utils.error_handling import (
    handle_parser_error, handle_file_operation, safe_operation,
    FileReadError, EncodingError, SearchToolsError
)

def test_error_handling():
    """Test the error handling utilities"""
    print("Testing error handling utilities...")

    # Test safe_operation
    print("\nTesting safe_operation...")

    def operation_that_succeeds():
        return "Success"

    def operation_that_fails():
        raise ValueError("This operation failed")

    # Create a policy for testing
    from searchtools.utils.error_handling import ErrorHandlingPolicy, ErrorAction, LogLevel

    test_policy = ErrorHandlingPolicy(
        action=ErrorAction.RETURN_DEFAULT,
        log_level=LogLevel.ERROR,
        default_value="Default",
        include_traceback=True
    )

    result1 = safe_operation(
        operation_that_succeeds,
        "Operation failed",
        policy=test_policy
    )
    print(f"Result of successful operation: {result1}")

    result2 = safe_operation(
        operation_that_fails,
        "Operation failed",
        policy=test_policy
    )
    print(f"Result of failed operation: {result2}")

    # Test handle_file_operation decorator
    print("\nTesting handle_file_operation decorator...")

    @handle_file_operation
    def read_file(file_path):
        with open(file_path, 'r') as f:
            return f.read()

    # Create a test file
    with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
        f.write("Test content")
        temp_file = f.name

    try:
        # Test with existing file
        content = read_file(temp_file)
        print(f"Content of existing file: {content}")

        # Test with non-existing file
        try:
            content = read_file("non_existing_file.txt")
            print(f"Content of non-existing file: {content}")
        except FileReadError as e:
            print(f"Caught FileReadError: {e}")
    finally:
        os.unlink(temp_file)

    # Test file_utils functions
    print("\nTesting file_utils functions...")

    # Test create_temp_dir
    temp_dir = create_temp_dir()
    print(f"Created temporary directory: {temp_dir}")

    # Test cleanup_temp_dir
    cleanup_temp_dir(temp_dir)
    print(f"Cleaned up temporary directory: {temp_dir}")

    # Test safe_read_text with non-existing file
    try:
        lines = safe_read_text("non_existing_file.txt")
        print(f"Read {len(lines)} lines from non-existing file")
    except FileReadError as e:
        print(f"Caught FileReadError from safe_read_text: {e}")

    print("\nAll tests completed.")

if __name__ == "__main__":
    test_error_handling()
