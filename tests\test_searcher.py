"""
Test script for TextSearcher refactoring
"""

from searchtools.core.searcher import TextSearcher

def test_search_text():
    """Test the search_text method"""
    print("Testing search_text method...")
    
    # Test simple substring search
    text = "This is a test string containing the word hello in it."
    keywords = ["hello", "world"]
    result = TextSearcher.search_text(text, keywords)
    print(f"Simple search result: {result}")
    
    # Test case-sensitive search
    text = "This is a test string containing the word Hello in it."
    keywords = ["hello"]
    result_case_insensitive = TextSearcher.search_text(text, keywords, case_sensitive=False)
    result_case_sensitive = TextSearcher.search_text(text, keywords, case_sensitive=True)
    print(f"Case-insensitive search: {result_case_insensitive}")
    print(f"Case-sensitive search: {result_case_sensitive}")
    
    # Test whole word search
    text = "This is a test string containing the word hello in it."
    keywords = ["test", "hello"]
    result_normal = TextSearcher.search_text(text, keywords)
    result_whole_word = TextSearcher.search_text(text, keywords, whole_word=True)
    print(f"Normal search: {result_normal}")
    print(f"Whole word search: {result_whole_word}")
    
    # Test regex search
    text = "This is a test string containing the word hello123 in it."
    keywords = ["hello\\d+"]
    result_normal = TextSearcher.search_text(text, keywords)
    result_regex = TextSearcher.search_text(text, keywords, use_regex=True)
    print(f"Normal search with regex pattern: {result_normal}")
    print(f"Regex search: {result_regex}")

def test_search_with_context():
    """Test the search_with_context method"""
    print("\nTesting search_with_context method...")
    
    # Test simple context extraction
    text = "This is a test string containing the word hello in it. This is additional text to provide more context."
    keywords = ["hello"]
    result = TextSearcher.search_with_context(text, keywords, context_chars=20)
    print(f"Context search result: {result}")
    
    # Test regex context extraction
    text = "This is a test string containing the word hello123 in it. This is additional text to provide more context."
    keywords = ["hello\\d+"]
    result = TextSearcher.search_with_context(text, keywords, use_regex=True, context_chars=20)
    print(f"Regex context search result: {result}")
    
    # Test whole word context extraction
    text = "This is a test string containing the word hello in it. This is additional text to provide more context."
    keywords = ["test", "word"]
    result = TextSearcher.search_with_context(text, keywords, whole_word=True, context_chars=20)
    print(f"Whole word context search result: {result}")

if __name__ == "__main__":
    test_search_text()
    test_search_with_context()
    print("\nAll tests completed successfully!")
