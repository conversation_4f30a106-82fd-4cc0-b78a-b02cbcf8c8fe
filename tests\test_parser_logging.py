"""
Test script for parser logging improvements
"""

import os
import sys
from searchtools.parsers.pdf_parser import PDFParser
from searchtools.parsers.excel_parser import ExcelParser
from searchtools.parsers.doc_parser import <PERSON>Parser

def test_parser_logging():
    """Test the parser logging improvements"""
    print("Testing parser logging improvements...")
    
    # Test PDF parser logging
    print("\nTesting PDF parser logging...")
    pdf_results = PDFParser.parse(
        file_path="non_existent.pdf",
        file_name="non_existent.pdf",
        keywords=["test"]
    )
    print(f"PDF parser returned {len(pdf_results)} results")
    
    # Test Excel parser logging
    print("\nTesting Excel parser logging...")
    excel_results = ExcelParser.parse(
        file_path="non_existent.xlsx",
        file_name="non_existent.xlsx",
        keywords=["test"]
    )
    print(f"Excel parser returned {len(excel_results)} results")
    
    # Test DOC parser logging
    print("\nTesting DOC parser logging...")
    doc_results = DocParser.parse(
        file_path="non_existent.doc",
        file_name="non_existent.doc",
        keywords=["test"]
    )
    print(f"DOC parser returned {len(doc_results)} results")
    
    print("\nAll tests completed.")

if __name__ == "__main__":
    test_parser_logging()
