2025-05-05 10:30:48,720 - INFO - PyPDF2 configured. PDF parsing support enabled.
2025-05-05 10:30:48,762 - INFO - win32com configured. DOC parsing support enabled.
2025-05-05 10:30:48,762 - DEBUG - antiword not available as fallback for DOC files.
2025-05-05 10:30:49,068 - INFO - Application started
2025-05-05 10:30:49,074 - INFO - Custom styling applied
2025-05-05 10:30:49,091 - INFO - rarfile configured. RAR parsing support enabled.
2025-05-05 10:30:49,443 - INFO - py7zr configured. 7z archive parsing support enabled.
2025-05-05 10:30:49,443 - DEBUG - Registered parser: Archive Parser
2025-05-05 10:30:49,445 - DEBUG - Registered parser: CSV Parser
2025-05-05 10:30:49,445 - DEBUG - Registered parser: DOC Parser
2025-05-05 10:30:49,446 - DEBUG - Registered parser: DOCX Parser
2025-05-05 10:30:49,446 - DEBUG - Registered parser: Excel Parser
2025-05-05 10:30:49,446 - DEBUG - Registered parser: ODS Parser
2025-05-05 10:30:49,447 - DEBUG - Registered parser: PDF Parser
2025-05-05 10:30:49,447 - DEBUG - Registered parser: Text Parser
2025-05-05 10:30:49,447 - INFO - Discovered 8 parser plugins
2025-05-05 10:30:49,511 - INFO - Looking for logo at (method 1): C:\Users\<USER>\Desktop\L457\SearchTools-v1\icon.ico
2025-05-05 10:30:49,512 - INFO - Logo file found at: C:\Users\<USER>\Desktop\L457\SearchTools-v1\icon.ico
2025-05-05 10:30:49,664 - INFO - Image opened, mode: RGBA, size: (159, 31)
2025-05-05 10:30:49,664 - INFO - Resized image to 100x19 to maintain aspect ratio
2025-05-05 10:30:49,677 - INFO - Converted to PhotoImage
2025-05-05 10:30:49,684 - INFO - Bottom logo loaded successfully
2025-05-05 10:30:49,901 - INFO - UI initialized
