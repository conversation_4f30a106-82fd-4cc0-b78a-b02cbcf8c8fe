# SearchTools

A modular file search application for finding keywords across various file types.

## Features

- Search for keywords across multiple file types:
  - Text files (.txt)
  - Excel files (.xlsx, .xls)
  - CSV files
  - Word documents (.docx) - requires python-docx
  - PDF files - requires PyPDF2
  - Archive files (.zip, .rar, .7z) - requires appropriate libraries

- Modern, user-friendly interface
- Multi-threaded search for better performance
- Detailed view of search results
- Export results to Excel or CSV
- Comprehensive logging

## Installation

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/SearchTools.git
   cd SearchTools
   ```

2. Install the required dependencies:
   ```
   pip install -r requirements.txt
   ```

3. Optional dependencies:
   - For DOCX support: `pip install python-docx`
   - For PDF support: `pip install PyPDF2`
   - For 7z support: `pip install py7zr`
   - For RAR support: `pip install rarfile` (also requires unrar executable)

## Usage

1. Run the application:
   ```
   python main.py
   ```

2. Select a root folder to scan
3. Enter keywords (comma-separated)
4. Click "Start Scan"
5. View results in the table
6. Double-click on a result to see details
7. Export results to Excel or CSV

## Project Structure

- `main.py` - Entry point
- `config.py` - Configuration settings
- `searchtools/` - Main package
  - `ui/` - UI components
  - `core/` - Core functionality
  - `parsers/` - File parsers
  - `utils/` - Utility functions
- `logs/` - Log files

## Customization

You can customize the application by modifying the `config.py` file:

- Change UI colors
- Adjust thread count
- Modify file size limits
- Configure logging settings

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Credits

Created by S4NG-7
