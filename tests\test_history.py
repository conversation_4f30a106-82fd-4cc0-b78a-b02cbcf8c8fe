"""
Unit tests for search history
"""

import os
import unittest
import tempfile
import json
from typing import List, Dict, Any

from searchtools.core.history import SearchHistory

class TestHistory(unittest.TestCase):
    """
    Test case for search history.
    """
    
    def setUp(self):
        """
        Set up the test case.
        """
        # Create a temporary file for the history
        fd, self.history_file = tempfile.mkstemp()
        os.close(fd)
        
        # Create a history instance
        self.history = SearchHistory(self.history_file)
    
    def tearDown(self):
        """
        Clean up after the test case.
        """
        # Remove the temporary file
        if os.path.exists(self.history_file):
            os.unlink(self.history_file)
    
    def test_add_search(self):
        """
        Test adding a search to the history.
        """
        # Add a search
        self.history.add_search("/test/folder", ["test", "keyword"], False)
        
        # Check that the search was added
        entries = self.history.get_history()
        self.assertEqual(len(entries), 1, "Search was not added to history")
        
        # Check the entry
        entry = entries[0]
        self.assertEqual(entry["folder_path"], "/test/folder", "Folder path is incorrect")
        self.assertEqual(entry["keywords"], ["test", "keyword"], "Keywords are incorrect")
        self.assertEqual(entry["case_sensitive"], False, "Case sensitivity is incorrect")
        self.assertIn("timestamp", entry, "Timestamp is missing")
    
    def test_duplicate_search(self):
        """
        Test adding a duplicate search to the history.
        """
        # Add a search twice
        self.history.add_search("/test/folder", ["test", "keyword"], False)
        self.history.add_search("/test/folder", ["test", "keyword"], False)
        
        # Check that only one entry was added
        entries = self.history.get_history()
        self.assertEqual(len(entries), 1, "Duplicate search was added to history")
    
    def test_clear_history(self):
        """
        Test clearing the history.
        """
        # Add some searches
        self.history.add_search("/test/folder1", ["test1"], False)
        self.history.add_search("/test/folder2", ["test2"], True)
        
        # Clear the history
        self.history.clear_history()
        
        # Check that the history is empty
        entries = self.history.get_history()
        self.assertEqual(len(entries), 0, "History was not cleared")
    
    def test_save_load_history(self):
        """
        Test saving and loading the history.
        """
        # Add some searches
        self.history.add_search("/test/folder1", ["test1"], False)
        self.history.add_search("/test/folder2", ["test2"], True)
        
        # Save the history
        self.history.save_history()
        
        # Create a new history instance with the same file
        new_history = SearchHistory(self.history_file)
        
        # Check that the history was loaded
        entries = new_history.get_history()
        self.assertEqual(len(entries), 2, "History was not loaded correctly")
        
        # Check the entries
        self.assertEqual(entries[0]["folder_path"], "/test/folder1", "Folder path is incorrect")
        self.assertEqual(entries[0]["keywords"], ["test1"], "Keywords are incorrect")
        self.assertEqual(entries[0]["case_sensitive"], False, "Case sensitivity is incorrect")
        
        self.assertEqual(entries[1]["folder_path"], "/test/folder2", "Folder path is incorrect")
        self.assertEqual(entries[1]["keywords"], ["test2"], "Keywords are incorrect")
        self.assertEqual(entries[1]["case_sensitive"], True, "Case sensitivity is incorrect")
    
    def test_formatted_history(self):
        """
        Test getting formatted history entries.
        """
        # Add some searches
        self.history.add_search("/test/folder1", ["test1"], False)
        self.history.add_search("/test/folder2", ["test2"], True)
        
        # Get formatted history
        formatted = self.history.get_formatted_history()
        
        # Check the formatted entries
        self.assertEqual(len(formatted), 2, "Wrong number of formatted entries")
        
        # Check that the entries contain the expected information
        self.assertIn("/test/folder2", formatted[0], "Folder path missing from formatted entry")
        self.assertIn("test2", formatted[0], "Keyword missing from formatted entry")
        self.assertIn("Case Sensitive", formatted[0], "Case sensitivity missing from formatted entry")
        
        self.assertIn("/test/folder1", formatted[1], "Folder path missing from formatted entry")
        self.assertIn("test1", formatted[1], "Keyword missing from formatted entry")
        self.assertIn("Case Insensitive", formatted[1], "Case sensitivity missing from formatted entry")

if __name__ == "__main__":
    unittest.main()
