2025-05-05 10:25:57,311 - INFO - PyPDF2 configured. PDF parsing support enabled.
2025-05-05 10:25:57,350 - INFO - win32com configured. DOC parsing support enabled.
2025-05-05 10:25:57,352 - DEBUG - antiword not available as fallback for DOC files.
2025-05-05 10:25:57,750 - INFO - Application started
2025-05-05 10:25:57,766 - INFO - Custom styling applied
2025-05-05 10:25:57,800 - INFO - rarfile configured. RAR parsing support enabled.
2025-05-05 10:25:58,152 - INFO - py7zr configured. 7z archive parsing support enabled.
2025-05-05 10:25:58,152 - DEBUG - Registered parser: Archive Parser
2025-05-05 10:25:58,152 - DEBUG - Registered parser: CSV Parser
2025-05-05 10:25:58,155 - DEBUG - Registered parser: DOC Parser
2025-05-05 10:25:58,155 - DEBUG - Registered parser: DOCX Parser
2025-05-05 10:25:58,155 - DEBUG - Registered parser: Excel Parser
2025-05-05 10:25:58,156 - DEBUG - Registered parser: ODS Parser
2025-05-05 10:25:58,156 - DEBUG - Registered parser: PDF Parser
2025-05-05 10:25:58,156 - DEBUG - Registered parser: Text Parser
2025-05-05 10:25:58,158 - INFO - Discovered 8 parser plugins
2025-05-05 10:25:58,223 - INFO - Looking for logo at (method 1): C:\Users\<USER>\Desktop\L457\SearchTools-v1\icon.ico
2025-05-05 10:25:58,224 - INFO - Logo file found at: C:\Users\<USER>\Desktop\L457\SearchTools-v1\icon.ico
2025-05-05 10:25:58,392 - INFO - Image opened, mode: RGBA, size: (159, 31)
2025-05-05 10:25:58,392 - INFO - Resized image to 100x19 to maintain aspect ratio
2025-05-05 10:25:58,407 - INFO - Converted to PhotoImage
2025-05-05 10:25:58,418 - INFO - Bottom logo loaded successfully
2025-05-05 10:25:58,674 - INFO - UI initialized
2025-05-05 10:26:11,160 - INFO - Window closed by user
2025-05-05 10:26:11,250 - INFO - Application exited
