"""
Main window for the SearchTools application
"""

import os
import sys
import time
import queue
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
from PIL import Image, ImageTk

from searchtools.utils.logging import logger, TextHandler
from searchtools.core.scanner import Scanner
from searchtools.core.result import SearchR<PERSON>ult
from searchtools.parsers.plugin_manager import plugin_manager
from searchtools.utils.error_handling import handle_ui_operation
from searchtools.utils.events import EventType, event_dispatcher
import config

# We'll use the standard tkinter styling without external theme packages

class MainWindow:
    """
    Main window for the SearchTools application.
    """

    def __init__(self, root):
        """
        Initialize the main window.

        Args:
            root (tk.Tk): Root window
        """
        self.root = root
        self.scanner = Scanner()
        self.results = []

        # Initialize result queue for thread-safe updates
        self.result_queue = queue.Queue()
        self.update_timer = None

        # Set up the root window
        self.root.title(f"{config.APP_NAME} v{config.APP_VERSION}")
        self.root.geometry(config.DEFAULT_WINDOW_SIZE)
        self.root.minsize(config.UI_MIN_WINDOW_WIDTH, config.UI_MIN_WINDOW_HEIGHT)

        # Set up window close handler
        self.root.protocol("WM_DELETE_WINDOW", self._on_close)

        # Apply custom styling
        self._apply_styling()

        # Set up scanner callbacks (legacy approach)
        self.scanner.set_callbacks(
            progress_callback=self.update_progress,
            status_callback=self.update_status,
            result_callback=self.add_result,
            file_progress_callback=self.update_file_progress
        )

        # Subscribe to scanner events (new event-based approach)
        self._subscribe_to_events()

        # Initialize plugin manager
        plugin_manager.discover_parsers()

        # Create the main frame
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=config.UI_MAIN_PADDING, pady=config.UI_MAIN_PADDING)

        # Create the UI components
        self._create_input_frame()
        self._create_results_frame()
        self._create_status_frame()

        # Initialize the UI
        self._initialize_ui()

        # Log initialization
        logger.info("UI initialized")

    @handle_ui_operation(error_message="Error applying UI styling")
    def _apply_styling(self):
        """
        Apply custom styling to the UI.
        """
        style = ttk.Style()

        # Apply styles to different widget types
        self._style_common_elements(style)
        self._style_treeview(style)
        self._style_progressbar(style)
        self._style_labelframes(style)

        # Set root window styling
        self._style_root_window()

        logger.info("Custom styling applied")

    def _style_common_elements(self, style):
        """
        Style common UI elements like frames, labels, buttons, etc.

        Args:
            style (ttk.Style): The ttk style object
        """
        # Configure common elements
        style.configure("TFrame", background=config.UI_COLORS["secondary_color"])
        style.configure("TLabel", background=config.UI_COLORS["secondary_color"],
                       foreground="black")
        style.configure("TButton", background=config.UI_COLORS["accent_color"],
                       foreground="black")
        style.configure("TCheckbutton", background=config.UI_COLORS["secondary_color"],
                       foreground="black")
        style.configure("TEntry", fieldbackground="white",
                       foreground="black")

        # Create a custom style for checkbuttons with white text
        style.configure("Custom.TCheckbutton",
                       background=config.UI_COLORS["secondary_color"],
                       foreground="white")

    def _style_treeview(self, style):
        """
        Style the treeview widget.

        Args:
            style (ttk.Style): The ttk style object
        """
        # Configure the Treeview
        style.configure("Treeview",
                       background="white",
                       foreground="black",
                       rowheight=config.TREE_ROW_HEIGHT)
        style.configure("Treeview.Heading",
                       background=config.UI_COLORS["accent_color"],
                       foreground="black",
                       font=(config.UI_FONT_FAMILY, config.UI_FONT_SIZE_NORMAL, "bold"))

    def _style_progressbar(self, style):
        """
        Style the progressbar widget and create a warning style variant.

        Args:
            style (ttk.Style): The ttk style object
        """
        # Configure the Progressbar
        style.configure("Horizontal.TProgressbar",
                       background=config.UI_COLORS["accent_color"],
                       troughcolor=config.UI_COLORS["tertiary_color"])

        # Create a warning style for the progress bar (used during stopping)
        try:
            # First, clone the existing Horizontal.TProgressbar layout
            style.layout("Stopping.Horizontal.TProgressbar",
                        style.layout("Horizontal.TProgressbar"))

            # Then configure the style with warning colors
            style.configure("Stopping.Horizontal.TProgressbar",
                           background=config.UI_COLORS["warning_color"],
                           troughcolor=config.UI_COLORS["tertiary_color"])
        except Exception as e:
            logger.warning(f"Error creating stopping progress bar style: {e}")

    def _style_labelframes(self, style):
        """
        Style the labelframe widgets.

        Args:
            style (ttk.Style): The ttk style object
        """
        # Configure LabelFrame
        style.configure("TLabelframe",
                       background=config.UI_COLORS["secondary_color"],
                       foreground="black")
        style.configure("TLabelframe.Label",
                       background=config.UI_COLORS["secondary_color"],
                       foreground="black",
                       font=(config.UI_FONT_FAMILY, config.UI_FONT_SIZE_NORMAL, "bold"))

        # Create a custom style for LabelFrame with white text
        style.configure("WhiteText.TLabelframe",
                       background=config.UI_COLORS["secondary_color"],
                       foreground="white")
        style.configure("WhiteText.TLabelframe.Label",
                       background=config.UI_COLORS["secondary_color"],
                       foreground="white",
                       font=(config.UI_FONT_FAMILY, config.UI_FONT_SIZE_NORMAL, "bold"))

    def _style_root_window(self):
        """
        Style the root window and set default widget colors.
        """
        # Set background color for the root window
        self.root.configure(background=config.UI_COLORS["bg_color"])

        # Set default text color for all widgets
        self.root.option_add("*foreground", "black")
        self.root.option_add("*background", "white")
        self.root.option_add("*Entry.background", "white")
        self.root.option_add("*Entry.foreground", "black")
        self.root.option_add("*Text.background", "white")
        self.root.option_add("*Text.foreground", "black")

    def _create_input_frame(self):
        """
        Create the input frame with folder selection and keyword input.
        """
        # Create the input frame with custom styling for the label
        self.input_frame = ttk.LabelFrame(self.main_frame, text="Search Settings", style="WhiteText.TLabelframe")
        self.input_frame.pack(fill=tk.X, padx=5, pady=5)

        # Create the folder selection frame
        folder_frame = ttk.Frame(self.input_frame)
        folder_frame.pack(fill=tk.X, padx=5, pady=5)

        # Folder selection
        folder_label = ttk.Label(folder_frame, text="Folder:", foreground="white", background=config.UI_COLORS["secondary_color"])
        folder_label.pack(side=tk.LEFT, padx=5)
        self.folder_var = tk.StringVar()
        self.folder_entry = ttk.Entry(folder_frame, textvariable=self.folder_var)
        self.folder_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        # Browse button
        self.browse_button = ttk.Button(folder_frame, text="Browse", command=self._browse_folder)
        self.browse_button.pack(side=tk.LEFT, padx=5)

        # Create the keywords frame
        keywords_frame = ttk.Frame(self.input_frame)
        keywords_frame.pack(fill=tk.X, padx=5, pady=5)

        # Keywords input
        keywords_label = ttk.Label(keywords_frame, text="Keywords:", foreground="white", background=config.UI_COLORS["secondary_color"])
        keywords_label.pack(side=tk.LEFT, padx=5)
        self.keywords_var = tk.StringVar()
        self.keywords_entry = ttk.Entry(keywords_frame, textvariable=self.keywords_var)
        self.keywords_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        # Create the buttons frame - this will contain buttons, checkboxes, and logo
        buttons_frame = ttk.Frame(self.input_frame)
        buttons_frame.pack(fill=tk.X, padx=5, pady=5)

        # Left side: Buttons
        # Start button
        self.start_button = ttk.Button(
            buttons_frame, text="Start Scan", command=self._start_scan
        )
        self.start_button.pack(side=tk.LEFT, padx=5)

        # Stop button
        self.stop_button = ttk.Button(
            buttons_frame, text="Stop Scan", command=self._stop_scan, state=tk.DISABLED
        )
        self.stop_button.pack(side=tk.LEFT, padx=5)

        # Export button
        self.export_button = ttk.Button(
            buttons_frame, text="Export Results", command=self._export_results, state=tk.DISABLED
        )
        self.export_button.pack(side=tk.LEFT, padx=5)

        # Clear button
        self.clear_button = ttk.Button(
            buttons_frame, text="Clear Results", command=self._clear_results, state=tk.DISABLED
        )
        self.clear_button.pack(side=tk.LEFT, padx=5)

        # Middle section (green area): Checkboxes
        # Add a small spacer to separate buttons from checkboxes
        ttk.Frame(buttons_frame, width=20).pack(side=tk.LEFT)

        # Search options in the green area
        self.case_sensitive_var = tk.BooleanVar(value=False)
        self.case_sensitive_check = ttk.Checkbutton(
            buttons_frame, text="Case Sensitive", variable=self.case_sensitive_var,
            style="Custom.TCheckbutton"
        )
        self.case_sensitive_check.pack(side=tk.LEFT, padx=5)

        self.regex_var = tk.BooleanVar(value=False)
        self.regex_check = ttk.Checkbutton(
            buttons_frame, text="Use Regex", variable=self.regex_var,
            style="Custom.TCheckbutton"
        )
        self.regex_check.pack(side=tk.LEFT, padx=5)

        self.whole_word_var = tk.BooleanVar(value=False)
        self.whole_word_check = ttk.Checkbutton(
            buttons_frame, text="Whole Word", variable=self.whole_word_var,
            style="Custom.TCheckbutton"
        )
        self.whole_word_check.pack(side=tk.LEFT, padx=5)

        # Right side (red area): Logo
        # Add spacer to push logo to the right
        spacer = ttk.Frame(buttons_frame)
        spacer.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Add the logo to the right side (red circled area)
        try:
            # Load the logo image from the root folder of the application
            # Try multiple methods to find the root directory
            try:
                # Method 1: Use sys.argv[0] (the script that was used to invoke the program)
                root_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
                logo_path = os.path.join(root_dir, "icon.ico")
                logger.info(f"Looking for logo at (method 1): {logo_path}")

                # If the file doesn't exist, try other methods
                if not os.path.exists(logo_path):
                    # Method 2: Go up from the current module's directory
                    root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                    logo_path = os.path.join(root_dir, "icon.ico")
                    logger.info(f"Looking for logo at (method 2): {logo_path}")

                    # If still not found, try one more method
                    if not os.path.exists(logo_path):
                        # Method 3: Use the current working directory
                        root_dir = os.getcwd()
                        logo_path = os.path.join(root_dir, "icon.ico")
                        logger.info(f"Looking for logo at (method 3): {logo_path}")
            except Exception as e:
                # Fallback to the module directory method if any error occurs
                logger.error(f"Error finding root directory: {e}")
                root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                logo_path = os.path.join(root_dir, "icon.ico")
                logger.info(f"Looking for logo at (fallback): {logo_path}")

            if os.path.exists(logo_path):
                logger.info(f"Logo file found at: {logo_path}")
                try:
                    # Open and resize the image
                    logo_img = Image.open(logo_path)
                    logger.info(f"Image opened, mode: {logo_img.mode}, size: {logo_img.size}")

                    # Convert to RGBA mode to ensure transparency is handled properly
                    if logo_img.mode != 'RGBA':
                        logo_img = logo_img.convert('RGBA')
                        logger.info("Converted image to RGBA mode")

                    # Calculate new dimensions to maintain aspect ratio
                    original_width, original_height = logo_img.size
                    target_width = 100  # Wider to maintain aspect ratio
                    target_height = int(original_height * (target_width / original_width))

                    # Resize to a size that fits the bottom area while maintaining aspect ratio
                    logo_img = logo_img.resize((target_width, target_height), Image.LANCZOS)
                    logger.info(f"Resized image to {target_width}x{target_height} to maintain aspect ratio")

                    # Convert to PhotoImage for Tkinter
                    self.bottom_logo_photo = ImageTk.PhotoImage(logo_img)
                    logger.info("Converted to PhotoImage")

                    # Create a label to display the logo
                    bottom_logo_label = ttk.Label(
                        buttons_frame,
                        image=self.bottom_logo_photo,
                        background=config.UI_COLORS["secondary_color"]
                    )
                    bottom_logo_label.pack(side=tk.RIGHT, padx=15, pady=5)

                    logger.info("Bottom logo loaded successfully")
                except Exception as e:
                    logger.error(f"Error processing logo image: {e}")
                    # Fall back to text logo
                    self._create_text_logo(buttons_frame)
            else:
                logger.error(f"Logo file not found at: {logo_path}")
                # Fall back to text logo
                self._create_text_logo(buttons_frame)
        except Exception as e:
            logger.error(f"Error loading bottom logo: {e}")
            # Fall back to text logo
            self._create_text_logo(buttons_frame)

    def _create_text_logo(self, parent_frame):
        """
        Create a text-based logo as a fallback.

        Args:
            parent_frame: The parent frame to add the logo to
        """
        try:
            # Create a text label for the logo
            logo_text = ttk.Label(
                parent_frame,
                text="Techlab CTI",
                font=("Arial", 16, "bold"),
                foreground="white",
                background=config.UI_COLORS["secondary_color"]
            )
            logo_text.pack(side=tk.RIGHT, padx=15, pady=5)
            logger.info("Text logo fallback added successfully")
        except Exception as e:
            logger.error(f"Error creating text logo fallback: {e}")

    def _create_results_frame(self):
        """
        Create the results frame with a treeview for displaying search results.
        """
        # Create the results frame
        self.results_frame = ttk.LabelFrame(self.main_frame, text="Search Results", style="WhiteText.TLabelframe")
        self.results_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create the treeview
        self.tree = ttk.Treeview(
            self.results_frame,
            columns=("file", "path", "keyword", "context"),
            show="headings"
        )

        # Set column headings with custom style to ensure visibility
        self.tree.heading("file", text="File")
        self.tree.heading("path", text="Path")
        self.tree.heading("keyword", text="Keyword")
        self.tree.heading("context", text="Context")

        # Force the heading text to be visible
        style = ttk.Style()
        style.configure("Treeview.Heading", foreground="black", font=("TkDefaultFont", 10, "bold"))

        # Set column widths
        self.tree.column("file", width=150)
        self.tree.column("path", width=300)
        self.tree.column("keyword", width=100)
        self.tree.column("context", width=400)

        # Add scrollbars
        vsb = ttk.Scrollbar(self.results_frame, orient="vertical", command=self.tree.yview)
        hsb = ttk.Scrollbar(self.results_frame, orient="horizontal", command=self.tree.xview)
        self.tree.configure(yscrollcommand=vsb.set, xscrollcommand=hsb.set)

        # Pack the treeview and scrollbars
        vsb.pack(side=tk.RIGHT, fill=tk.Y)
        hsb.pack(side=tk.BOTTOM, fill=tk.X)
        self.tree.pack(fill=tk.BOTH, expand=True)

        # Bind double-click event
        self.tree.bind("<Double-1>", self._show_result_details)

    def _create_status_frame(self):
        """
        Create the status frame with progress bars and status labels.
        """
        # Create the status frame
        self.status_frame = ttk.Frame(self.main_frame)
        self.status_frame.pack(fill=tk.X, padx=5, pady=5)

        # Create the progress bars frame (3 bars on the same line)
        progress_frame = ttk.Frame(self.status_frame)
        progress_frame.pack(fill=tk.X, padx=5, pady=2)

        # Configure grid columns to have equal width
        progress_frame.columnconfigure(0, weight=1)  # Overall progress
        progress_frame.columnconfigure(1, weight=1)  # Files progress
        progress_frame.columnconfigure(2, weight=1)  # Archives progress

        # --- First progress bar (Overall) ---
        # Create a container frame for the first progress bar
        overall_container = ttk.Frame(progress_frame)
        overall_container.grid(row=0, column=0, padx=5, sticky="ew")

        # Label for the first progress bar
        progress1_label = ttk.Label(overall_container, text="Overall:", foreground="white",
                                   background=config.UI_COLORS["secondary_color"])
        progress1_label.pack(side=tk.TOP, anchor="w", pady=(0, 2))

        # Frame to hold the progress bar and percentage
        overall_frame = ttk.Frame(overall_container)
        overall_frame.pack(fill=tk.X, expand=True)

        # Create the progress bar
        self.overall_progress = ttk.Progressbar(overall_frame, length=100, mode="determinate")
        self.overall_progress.pack(fill=tk.X, expand=True)

        # Create percentage label and position it over the progress bar
        self.overall_percent_var = tk.StringVar(value="0%")
        self.overall_percent = tk.Label(overall_frame, textvariable=self.overall_percent_var,
                                       background=config.UI_COLORS["tertiary_color"], foreground="#ffffff",
                                       font=(config.UI_FONT_FAMILY, config.UI_FONT_SIZE_NORMAL, "bold"),
                                       relief="flat", borderwidth=0, padx=2, pady=0)
        self.overall_percent.place(relx=0.5, rely=0.5, anchor=tk.CENTER)

        # --- Second progress bar (Files) ---
        # Create a container frame for the second progress bar
        files_container = ttk.Frame(progress_frame)
        files_container.grid(row=0, column=1, padx=5, sticky="ew")

        # Label for the second progress bar
        progress2_label = ttk.Label(files_container, text="Files:", foreground="white",
                                   background=config.UI_COLORS["secondary_color"])
        progress2_label.pack(side=tk.TOP, anchor="w", pady=(0, 2))

        # Frame to hold the progress bar and percentage
        files_frame = ttk.Frame(files_container)
        files_frame.pack(fill=tk.X, expand=True)

        # Create the progress bar
        self.file_progress = ttk.Progressbar(files_frame, length=100, mode="determinate")
        self.file_progress.pack(fill=tk.X, expand=True)

        # Create percentage label and position it over the progress bar
        self.file_percent_var = tk.StringVar(value="0%")
        self.file_percent = tk.Label(files_frame, textvariable=self.file_percent_var,
                                    background=config.UI_COLORS["tertiary_color"], foreground="#ffffff",
                                    font=(config.UI_FONT_FAMILY, config.UI_FONT_SIZE_NORMAL, "bold"),
                                    relief="flat", borderwidth=0, padx=2, pady=0)
        self.file_percent.place(relx=0.5, rely=0.5, anchor=tk.CENTER)

        # --- Third progress bar (Archives) ---
        # Create a container frame for the third progress bar
        archives_container = ttk.Frame(progress_frame)
        archives_container.grid(row=0, column=2, padx=5, sticky="ew")

        # Label for the third progress bar
        progress3_label = ttk.Label(archives_container, text="Archives:", foreground="white",
                                   background=config.UI_COLORS["secondary_color"])
        progress3_label.pack(side=tk.TOP, anchor="w", pady=(0, 2))

        # Frame to hold the progress bar and percentage
        archives_frame = ttk.Frame(archives_container)
        archives_frame.pack(fill=tk.X, expand=True)

        # Create the progress bar
        self.progress3 = ttk.Progressbar(archives_frame, length=100, mode="determinate")
        self.progress3.pack(fill=tk.X, expand=True)

        # Create percentage label and position it over the progress bar
        self.archives_percent_var = tk.StringVar(value="0%")
        self.archives_percent = tk.Label(archives_frame, textvariable=self.archives_percent_var,
                                        background=config.UI_COLORS["tertiary_color"], foreground="#ffffff",
                                        font=(config.UI_FONT_FAMILY, config.UI_FONT_SIZE_NORMAL, "bold"),
                                        relief="flat", borderwidth=0, padx=2, pady=0)
        self.archives_percent.place(relx=0.5, rely=0.5, anchor=tk.CENTER)

        # Create the status label frame
        status_label_frame = ttk.Frame(self.status_frame)
        status_label_frame.pack(fill=tk.X, padx=5, pady=2)

        # Status label
        status_text_label = ttk.Label(status_label_frame, text="Status:", foreground="white", background=config.UI_COLORS["secondary_color"])
        status_text_label.pack(side=tk.LEFT, padx=5)
        self.status_var = tk.StringVar(value="Ready")
        self.status_label = ttk.Label(status_label_frame, textvariable=self.status_var, foreground="white", background=config.UI_COLORS["secondary_color"])
        self.status_label.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        # Create the log frame
        log_frame = ttk.LabelFrame(self.main_frame, text="Log", style="WhiteText.TLabelframe")
        log_frame.pack(fill=tk.X, padx=5, pady=5)

        # Create the log text widget
        self.log_text = tk.Text(log_frame, height=5, wrap=tk.WORD, bg="white", fg="black")
        self.log_text.pack(fill=tk.X, padx=5, pady=5)

        # Configure text tags for different log levels
        self.log_text.tag_configure("error", foreground="red")
        self.log_text.tag_configure("warning", foreground="orange")
        self.log_text.tag_configure("info", foreground="blue")

        # Add a text handler to the logger
        self.text_handler = TextHandler(self.log_text)
        logger.addHandler(self.text_handler)

    def _initialize_ui(self):
        """
        Initialize the UI state.
        """
        # Set initial values
        self.folder_var.set(os.path.expanduser("~"))
        self.keywords_var.set("")

        # Reset all progress bars and percentage labels
        self.overall_progress["value"] = 0
        self.overall_percent_var.set("0%")
        self.file_progress["value"] = 0
        self.file_percent_var.set("0%")
        self.progress3["value"] = 0
        self.archives_percent_var.set("0%")

        # Set initial label colors
        self._update_percent_label_color(self.overall_percent, 0)
        self._update_percent_label_color(self.file_percent, 0)
        self._update_percent_label_color(self.archives_percent, 0)

        # Update status
        self.update_status("Ready")

    def _browse_folder(self):
        """
        Open a file/folder browser dialog.
        """
        # Ask for a directory or file
        path = filedialog.askopenfilename(
            initialdir=os.path.dirname(self.folder_var.get()),
            title="Select Archive File to Scan",
            filetypes=[
                ("Archive Files", "*.zip *.rar *.7z"),
                ("ZIP Files", "*.zip"),
                ("RAR Files", "*.rar"),
                ("7Z Files", "*.7z"),
                ("All Files", "*.*")
            ]
        )

        if not path:
            # If user cancels file selection, try folder selection
            folder = filedialog.askdirectory(
                initialdir=self.folder_var.get(),
                title="Select Folder to Scan"
            )

            if folder:
                self.folder_var.set(folder)
        else:
            self.folder_var.set(path)

    def _start_scan(self):
        """
        Start the scan process.
        """
        # Get the folder and keywords
        folder = self.folder_var.get()
        keywords_text = self.keywords_var.get()

        # Import ArchiveParser to check if the path is an archive
        from searchtools.parsers.archive_parser import ArchiveParser

        # Validate inputs - check if it's a valid directory OR a valid archive file
        if not folder:
            messagebox.showerror("Error", "Please select a valid folder or file")
            return

        # Check if it's a directory or a valid archive file
        is_valid_dir = os.path.isdir(folder)
        is_valid_archive = os.path.isfile(folder) and ArchiveParser.is_archive(folder)

        if not is_valid_dir and not is_valid_archive:
            messagebox.showerror("Error", "Please select a valid folder or archive file (ZIP, RAR, 7Z)")
            return

        if not keywords_text:
            messagebox.showerror("Error", "Please enter at least one keyword")
            return

        # Parse keywords
        keywords = [k.strip() for k in keywords_text.split(',') if k.strip()]

        if not keywords:
            messagebox.showerror("Error", "Please enter at least one valid keyword")
            return

        # Clear previous results
        self._clear_results()

        # Update UI state
        self.start_button["state"] = tk.DISABLED
        self.stop_button["state"] = tk.NORMAL
        self.export_button["state"] = tk.DISABLED
        self.clear_button["state"] = tk.DISABLED

        # Reset all progress bars and percentage labels
        self.overall_progress["value"] = 0
        self.overall_percent_var.set("0%")
        self.file_progress["value"] = 0
        self.file_percent_var.set("0%")
        self.progress3["value"] = 0
        self.archives_percent_var.set("0%")

        # Reset label colors
        self._update_percent_label_color(self.overall_percent, 0)
        self._update_percent_label_color(self.file_percent, 0)
        self._update_percent_label_color(self.archives_percent, 0)

        # Update status
        self.update_status("Starting scan...")

        # Get search options
        case_sensitive = self.case_sensitive_var.get()
        use_regex = self.regex_var.get()
        whole_word = self.whole_word_var.get()

        # Log scan start
        logger.info(f"Starting scan of {folder}")
        logger.info(f"Keywords: {', '.join(keywords)}")
        logger.info(f"Options: case_sensitive={case_sensitive}, use_regex={use_regex}, whole_word={whole_word}")

        # Start the scan
        self.scanner.start_scan(folder, keywords, case_sensitive, use_regex, whole_word)

    def _stop_scan(self):
        """
        Stop the current scan.

        This method stops the current scan and updates the UI to reflect
        that the scan is being stopped. The actual stopping of the scan
        happens asynchronously as the scanner threads check the stop_scan flag.
        """
        if self.scanner.scan_in_progress:
            # Disable the stop button to prevent multiple clicks
            self.stop_button["state"] = tk.DISABLED

            # Change the button text to indicate stopping is in progress
            self.stop_button["text"] = "Stopping..."

            # Change the button appearance to indicate stopping
            style = ttk.Style()
            style.configure("Stopping.TButton",
                           background=config.UI_COLORS["warning_color"],
                           foreground="black")
            self.stop_button.configure(style="Stopping.TButton")

            # Update the current file progress to show stopping
            self.file_progress["value"] = 0

            # Change progress bar color to indicate stopping
            try:
                # First, clone the existing Horizontal.TProgressbar layout
                style.layout("Stopping.Horizontal.TProgressbar",
                            style.layout("Horizontal.TProgressbar"))

                # Then configure the style with new colors
                style.configure("Stopping.Horizontal.TProgressbar",
                               background=config.UI_COLORS["warning_color"],
                               troughcolor=config.UI_COLORS["tertiary_color"])

                # Apply the style to all progress bars
                self.overall_progress.configure(style="Stopping.Horizontal.TProgressbar")
                self.file_progress.configure(style="Stopping.Horizontal.TProgressbar")
                self.progress3.configure(style="Stopping.Horizontal.TProgressbar")
            except Exception as e:
                # If styling fails, just log the error but continue with the stop operation
                logger.error(f"Error applying stopping style to progress bars: {e}")
                # The stop operation should still work even if styling fails

            # Update UI to show stopping status with more detail
            current_file = self.scanner.current_file_name or "current file"
            self.update_status(f"Stopping scan... Finishing {current_file}. Please wait...")

            # Log the stop request
            logger.info("User requested to stop the scan")

            # Tell the scanner to stop
            self.scanner.stop_scanning()

            # Force UI update to show the stopping message and visual changes
            self.root.update_idletasks()

            # Start a thread to periodically update the UI during stopping
            threading.Thread(target=self._monitor_stop_progress, daemon=True).start()

    def _monitor_stop_progress(self):
        """
        Monitor the stopping progress and update the UI.
        This runs in a separate thread to keep the UI responsive.
        """
        start_time = time.time()
        dots = 1

        # Continue updating until the scan is no longer in progress or timeout
        while self.scanner.scan_in_progress and time.time() - start_time < config.STOP_SCAN_TIMEOUT + 5:
            # Update the status message with animated dots
            current_file = self.scanner.current_file_name or "current file"
            dots = (dots % 3) + 1  # Cycle through 1, 2, 3 dots
            dot_str = "." * dots
            self.update_status(f"Stopping scan{dot_str} Finishing {current_file}. Please wait{dot_str}")

            # Sleep briefly
            time.sleep(config.UI_ANIMATION_INTERVAL)

        # If scan is still in progress after timeout, show a more urgent message
        if self.scanner.scan_in_progress:
            self.update_status("Forcing scan to stop. This may take a moment...")

        # Reset progress bar style when done
        try:
            style = ttk.Style()
            style.configure("Horizontal.TProgressbar",
                           background=config.UI_COLORS["accent_color"],
                           troughcolor=config.UI_COLORS["tertiary_color"])
            self.overall_progress.configure(style="Horizontal.TProgressbar")
            self.file_progress.configure(style="Horizontal.TProgressbar")
            self.progress3.configure(style="Horizontal.TProgressbar")
        except Exception as e:
            logger.error(f"Error resetting progress bar styles: {e}")

    def _export_results(self):
        """
        Export the search results.
        """
        if not self.results:
            messagebox.showinfo("Info", "No results to export")
            return

        # Ask for the export file path
        file_path = filedialog.asksaveasfilename(
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("Excel files", "*.xlsx"), ("All files", "*.*")],
            title="Export Results"
        )

        if not file_path:
            return

        # Determine export format based on extension
        ext = os.path.splitext(file_path)[1].lower()

        try:
            if ext == ".csv":
                self._export_to_csv(file_path)
            elif ext == ".xlsx":
                self._export_to_excel(file_path)
            else:
                messagebox.showerror("Error", f"Unsupported file format: {ext}")
                return

            messagebox.showinfo("Success", f"Results exported to {file_path}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to export results: {e}")
            logger.error(f"Export error: {e}")

    def _export_to_csv(self, file_path):
        """
        Export results to a CSV file.

        Args:
            file_path (str): Path to the CSV file
        """
        import csv

        with open(file_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)

            # Write header
            writer.writerow(["File", "Path", "Keyword", "Context", "Location"])

            # Write data
            for result in self.results:
                writer.writerow([
                    result.file,
                    result.path,
                    result.keyword,
                    result.context,
                    result.location
                ])

        logger.info(f"Exported {len(self.results)} results to {file_path}")

    def _export_to_excel(self, file_path):
        """
        Export results to an Excel file.

        Args:
            file_path (str): Path to the Excel file
        """
        try:
            import pandas as pd

            # Create a DataFrame
            data = []
            for result in self.results:
                data.append({
                    "File": result.file,
                    "Path": result.path,
                    "Keyword": result.keyword,
                    "Context": result.context,
                    "Location": result.location
                })

            df = pd.DataFrame(data)

            # Export to Excel
            df.to_excel(file_path, index=False)

            logger.info(f"Exported {len(self.results)} results to {file_path}")

        except ImportError:
            messagebox.showerror("Error", "Pandas is required for Excel export")
            logger.error("Pandas not available for Excel export")

    def _clear_results(self):
        """
        Clear the search results.
        """
        # Clear the treeview
        for item in self.tree.get_children():
            self.tree.delete(item)

        # Clear the results list and queue
        self.results = []

        # Clear the queue
        while not self.result_queue.empty():
            try:
                self.result_queue.get_nowait()
            except queue.Empty:
                break

        # Clear the processed result IDs set
        if hasattr(self, 'processed_result_ids'):
            self.processed_result_ids.clear()

        # Cancel any pending update timer
        if self.update_timer is not None:
            self.root.after_cancel(self.update_timer)
            self.update_timer = None

        # Update UI state
        self.export_button["state"] = tk.DISABLED
        self.clear_button["state"] = tk.DISABLED

        # Log
        logger.info("Results cleared")

    def _show_result_details(self, _):
        """
        Show details for the selected result.

        Args:
            _: The event that triggered this method (unused)
        """
        # Get the selected item
        selection = self.tree.selection()
        if not selection:
            return

        # Get the result index from the item ID
        item_id = selection[0]
        try:
            # Check if it's our custom format "result_X"
            if item_id.startswith("result_"):
                index = int(item_id.split("_")[1])
            else:
                # Fallback for any existing items with old format
                try:
                    index = int(item_id[1:])  # Try the old format (remove 'I' prefix)
                except ValueError:
                    # If that fails, just use the first result as a last resort
                    logger.warning(f"Unrecognized item ID format: {item_id}, using first result")
                    index = 0

            # Validate the index is in range
            if 0 <= index < len(self.results):
                self._open_details_window(self.results[index])
            else:
                logger.error(f"Result index out of range: {index}, max: {len(self.results)-1}")
        except (ValueError, IndexError) as e:
            logger.error(f"Invalid result index: {item_id}, error: {e}")

    @handle_ui_operation(error_message="Error opening result details window")
    def _open_details_window(self, result):
        """
        Open a details window for the selected result.

        Args:
            result (SearchResult): The result to display
        """
        # Create the window and main frame
        details_window, details_frame = self._create_details_window(result)

        # Create file information section
        self._create_file_info_section(details_frame, result)

        # Create match information section
        self._create_match_info_section(details_frame, result)

        # Create full context section
        self._create_context_section(details_frame, result)

        # Add a close button
        close_button = ttk.Button(details_frame, text="Close", command=details_window.destroy)
        close_button.pack(pady=10)

    @handle_ui_operation(error_message="Error creating details window")
    def _create_details_window(self, result):
        """
        Create the details window and main frame.

        Args:
            result (SearchResult): The result to display

        Returns:
            tuple: (details_window, details_frame)
        """
        # Create a new top-level window
        details_window = tk.Toplevel(self.root)
        details_window.title(f"Result Details - {result.file}")
        details_window.geometry(config.DETAILS_WINDOW_SIZE)
        details_window.transient(self.root)
        details_window.grab_set()

        # Create a frame for the details
        details_frame = ttk.Frame(details_window, padding=10)
        details_frame.pack(fill=tk.BOTH, expand=True)

        return details_window, details_frame

    @handle_ui_operation(error_message="Error creating file information section")
    def _create_file_info_section(self, parent_frame, result):
        """
        Create the file information section in the details window.

        Args:
            parent_frame (ttk.Frame): The parent frame
            result (SearchResult): The result to display
        """
        # File information - with white text
        file_frame = ttk.LabelFrame(parent_frame, text="File Information", style="WhiteText.TLabelframe")
        file_frame.pack(fill=tk.X, padx=config.UI_SECTION_PADDING, pady=config.UI_SECTION_PADDING)

        # Create a grid layout for file information
        # Use a regular tk.Frame instead of ttk.Frame to have better control over colors
        grid_frame = tk.Frame(file_frame, bg=config.UI_COLORS["secondary_color"])
        grid_frame.pack(fill=tk.X, padx=config.UI_SECTION_PADDING, pady=config.UI_SECTION_PADDING)

        # File info with white text
        row = 0
        tk.Label(grid_frame, text="File:", fg="white", bg=config.UI_COLORS["secondary_color"]).grid(
            row=row, column=0, sticky=tk.W, padx=config.UI_SECTION_PADDING, pady=config.UI_ELEMENT_PADDING)
        tk.Label(grid_frame, text=result.file, fg="white", bg=config.UI_COLORS["secondary_color"]).grid(
            row=row, column=1, sticky=tk.W, padx=config.UI_SECTION_PADDING, pady=config.UI_ELEMENT_PADDING)

        row += 1
        tk.Label(grid_frame, text="Path:", fg="white", bg=config.UI_COLORS["secondary_color"]).grid(
            row=row, column=0, sticky=tk.W, padx=config.UI_SECTION_PADDING, pady=config.UI_ELEMENT_PADDING)
        path_label = tk.Label(grid_frame, text=result.path, fg="white", bg=config.UI_COLORS["secondary_color"])
        path_label.grid(row=row, column=1, sticky=tk.W, padx=config.UI_SECTION_PADDING, pady=config.UI_ELEMENT_PADDING)

        row += 1
        tk.Label(grid_frame, text="Keyword:", fg="white", bg=config.UI_COLORS["secondary_color"]).grid(
            row=row, column=0, sticky=tk.W, padx=config.UI_SECTION_PADDING, pady=config.UI_ELEMENT_PADDING)
        keyword_label = tk.Label(grid_frame, text=result.keyword, fg="white", bg=config.UI_COLORS["secondary_color"],
                                font=(config.UI_FONT_FAMILY, config.UI_FONT_SIZE_NORMAL, "bold"))
        keyword_label.grid(row=row, column=1, sticky=tk.W, padx=config.UI_SECTION_PADDING, pady=config.UI_ELEMENT_PADDING)

        # Add location if available
        if result.location:
            row += 1
            tk.Label(grid_frame, text="Location:", fg="white", bg=config.UI_COLORS["secondary_color"]).grid(
                row=row, column=0, sticky=tk.W, padx=config.UI_SECTION_PADDING, pady=config.UI_ELEMENT_PADDING)
            tk.Label(grid_frame, text=result.location, fg="white", bg=config.UI_COLORS["secondary_color"]).grid(
                row=row, column=1, sticky=tk.W, padx=config.UI_SECTION_PADDING, pady=config.UI_ELEMENT_PADDING)

    @handle_ui_operation(error_message="Error creating match information section")
    def _create_match_info_section(self, parent_frame, result):
        """
        Create the match information section in the details window.

        Args:
            parent_frame (ttk.Frame): The parent frame
            result (SearchResult): The result to display
        """
        # Match information - show the line with the table name (with white text)
        match_frame = ttk.LabelFrame(parent_frame, text="Match Information", style="WhiteText.TLabelframe")
        match_frame.pack(fill=tk.X, padx=config.UI_SECTION_PADDING, pady=config.UI_SECTION_PADDING)

        # Extract the line containing the keyword
        match_line = self._extract_match_line(result)

        # Create a text widget for the match line
        match_text = tk.Text(match_frame, height=config.UI_MATCH_TEXT_HEIGHT, wrap=tk.WORD, bg="white", fg="black")
        match_text.pack(fill=tk.X, padx=config.UI_SECTION_PADDING, pady=config.UI_SECTION_PADDING)

        # Configure default text color
        match_text.configure(insertbackground="black")
        match_text.tag_configure("default", foreground="black")

        # Insert the match line with the default tag
        match_text.insert(tk.END, match_line, "default")

        # Highlight the keyword
        self._highlight_text(match_text, result.keyword)

        # Make the text widget read-only
        match_text.configure(state=tk.DISABLED)

    @handle_ui_operation(error_message="Error creating context section")
    def _create_context_section(self, parent_frame, result):
        """
        Create the full context section in the details window.

        Args:
            parent_frame (ttk.Frame): The parent frame
            result (SearchResult): The result to display
        """
        # Full context (with white text)
        context_frame = ttk.LabelFrame(parent_frame, text="Full Context", style="WhiteText.TLabelframe")
        context_frame.pack(fill=tk.BOTH, expand=True, padx=config.UI_SECTION_PADDING, pady=config.UI_SECTION_PADDING)

        # Create a text widget for the context
        context_text = tk.Text(context_frame, wrap=tk.WORD, bg="white", fg="black")
        context_text.pack(fill=tk.BOTH, expand=True, padx=config.UI_SECTION_PADDING, pady=config.UI_SECTION_PADDING)

        # Add a scrollbar
        scrollbar = ttk.Scrollbar(context_text, command=context_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        context_text.configure(yscrollcommand=scrollbar.set)

        # Configure default text color
        context_text.configure(insertbackground="black")
        context_text.tag_configure("default", foreground="black")

        # Insert the context with the default tag
        context_text.insert(tk.END, result.full_context, "default")

        # Highlight the keyword
        self._highlight_text(context_text, result.keyword)

        # Make the text widget read-only
        context_text.configure(state=tk.DISABLED)

    @handle_ui_operation(error_message="Error highlighting text")
    def _highlight_text(self, text_widget, keyword):
        """
        Highlight all occurrences of a keyword in a text widget.

        Args:
            text_widget (tk.Text): The text widget
            keyword (str): The keyword to highlight
        """
        try:
            start_idx = "1.0"
            while True:
                start_idx = text_widget.search(keyword, start_idx, stopindex=tk.END, nocase=True)
                if not start_idx:
                    break

                end_idx = f"{start_idx}+{len(keyword)}c"
                text_widget.tag_add("highlight", start_idx, end_idx)
                start_idx = end_idx

            # Configure the highlight tag
            text_widget.tag_configure("highlight",
                                     background=config.HIGHLIGHT_COLOR,
                                     foreground="black",
                                     font=(config.UI_FONT_FAMILY, config.UI_FONT_SIZE_NORMAL, "bold"))
        except Exception as e:
            logger.error(f"Error highlighting keyword in text: {e}")

    @handle_ui_operation(error_message="Error extracting match line")
    def _extract_match_line(self, result):
        """
        Extract the line containing the keyword from the full context.

        Args:
            result (SearchResult): The search result

        Returns:
            str: The line containing the keyword
        """
        # If we don't have full context, return the context
        if not result.full_context:
            return result.context

        # Split the full context into lines
        lines = result.full_context.splitlines()

        # Find the line containing the keyword
        for line in lines:
            if result.keyword.lower() in line.lower():
                return line

        # If no line contains the keyword, return the context
        return result.context

    @handle_ui_operation(error_message="Error updating progress bar")
    def update_progress(self, progress):
        """
        Update the overall progress bar.

        Args:
            progress (float): Progress value (0-100)
        """
        self.overall_progress["value"] = progress
        self.overall_percent_var.set(f"{int(progress)}%")

        # Update the label color based on progress
        self._update_percent_label_color(self.overall_percent, progress)

        self.root.update_idletasks()

    @handle_ui_operation(error_message="Error updating file progress bar")
    def update_file_progress(self, progress, _):
        """
        Update the file progress bar.

        Args:
            progress (float): Progress value (0-100)
            _: Name of the file being processed (unused)
        """
        self.file_progress["value"] = progress
        self.file_percent_var.set(f"{int(progress)}%")

        # Update the label color based on progress
        self._update_percent_label_color(self.file_percent, progress)

        self.root.update_idletasks()

    @handle_ui_operation(error_message="Error updating archives progress bar")
    def update_archives_progress(self, progress):
        """
        Update the archives progress bar (progress3).

        Args:
            progress (float): Progress value (0-100)
        """
        self.progress3["value"] = progress
        self.archives_percent_var.set(f"{int(progress)}%")

        # Update the label color based on progress
        self._update_percent_label_color(self.archives_percent, progress)

        self.root.update_idletasks()

    def _update_percent_label_color(self, label, _):
        """
        Update the color of a percentage label.

        Note: The progress parameter is kept for compatibility but not used
        as we now maintain a consistent appearance regardless of progress.

        Args:
            label (tk.Label): The label to update
            _ (float): Progress value (0-100) - not used but kept for compatibility
        """
        # Keep a consistent background color that matches the progress bar's background
        bg_color = config.UI_COLORS["tertiary_color"]
        fg_color = "#ffffff"  # White text for good contrast

        # Apply the styling - keep it simple and consistent
        label.configure(
            background=bg_color,
            foreground=fg_color,
            relief="flat",
            borderwidth=0
        )

    @handle_ui_operation(error_message="Error updating status message")
    def update_status(self, status):
        """
        Update the status label.

        Args:
            status (str): Status message
        """
        self.status_var.set(status)
        self.root.update_idletasks()

        # If the scan is complete, update UI state
        if "complete" in status.lower() or "stopped" in status.lower():
            self.start_button["state"] = tk.NORMAL
            self.stop_button["state"] = tk.DISABLED

            # Reset the stop button text and style
            self.stop_button["text"] = "Stop Scan"
            self.stop_button.configure(style="TButton")

            # Reset progress bar styles
            try:
                style = ttk.Style()
                style.configure("Horizontal.TProgressbar",
                               background=config.UI_COLORS["accent_color"],
                               troughcolor=config.UI_COLORS["tertiary_color"])
                self.overall_progress.configure(style="Horizontal.TProgressbar")
                self.file_progress.configure(style="Horizontal.TProgressbar")
                self.progress3.configure(style="Horizontal.TProgressbar")
            except Exception as e:
                logger.error(f"Error resetting progress bar styles: {e}")

            if self.results:
                self.export_button["state"] = tk.NORMAL
                self.clear_button["state"] = tk.NORMAL

    def _subscribe_to_events(self):
        """
        Subscribe to scanner events.
        """
        # Subscribe to progress update events
        event_dispatcher.subscribe(
            EventType.PROGRESS_UPDATE,
            self._handle_progress_event
        )

        # Subscribe to status update events
        event_dispatcher.subscribe(
            EventType.STATUS_UPDATE,
            self._handle_status_event
        )

        # Subscribe to result found events
        event_dispatcher.subscribe(
            EventType.RESULT_FOUND,
            self._handle_result_event
        )

        # Subscribe to file progress update events
        event_dispatcher.subscribe(
            EventType.FILE_PROGRESS_UPDATE,
            self._handle_file_progress_event
        )

    @handle_ui_operation(error_message="Error handling progress event")
    def _handle_progress_event(self, event):
        """
        Handle progress update events.

        Args:
            event (Event): Progress update event
        """
        progress = event.data.get("progress", 0)
        progress_type = event.data.get("type", "overall")

        # Update the appropriate progress bar based on the type
        if progress_type == "overall":
            self.update_progress(progress)
        elif progress_type == "files" or progress_type == "current_file":
            self.update_file_progress(progress, "")
        elif progress_type == "archives":
            self.update_archives_progress(progress)
        else:
            # Default to overall progress if type is not recognized
            self.update_progress(progress)

    @handle_ui_operation(error_message="Error handling status event")
    def _handle_status_event(self, event):
        """
        Handle status update events.

        Args:
            event (Event): Status update event
        """
        status = event.data.get("status", "")
        self.update_status(status)

    @handle_ui_operation(error_message="Error handling result event")
    def _handle_result_event(self, event):
        """
        Handle result found events.

        Args:
            event (Event): Result found event
        """
        result = event.data.get("result")
        if result:
            self.add_result(result)

    @handle_ui_operation(error_message="Error handling file progress event")
    def _handle_file_progress_event(self, event):
        """
        Handle file progress update events.

        Args:
            event (Event): File progress update event
        """
        progress = event.data.get("progress", 0)
        file_name = event.data.get("file_name", "")
        self.update_file_progress(progress, file_name)

    def _on_close(self):
        """
        Handle window close event.
        """
        # Stop any running scan
        if self.scanner.scan_in_progress:
            self.scanner.stop_scanning()
            logger.info("Scan stopped due to application exit")

        # Clean up any temporary directories
        self.scanner.cleanup_temp_dirs()

        # Unsubscribe from events
        event_dispatcher.unsubscribe(EventType.PROGRESS_UPDATE, self._handle_progress_event)
        event_dispatcher.unsubscribe(EventType.STATUS_UPDATE, self._handle_status_event)
        event_dispatcher.unsubscribe(EventType.RESULT_FOUND, self._handle_result_event)
        event_dispatcher.unsubscribe(EventType.FILE_PROGRESS_UPDATE, self._handle_file_progress_event)

        # Log application exit
        logger.info("Window closed by user")

        # Remove the text handler from the logger to prevent errors when the text widget is destroyed
        try:
            logger.removeHandler(self.text_handler)
        except (AttributeError, ValueError):
            pass

        # Destroy the root window
        self.root.destroy()

    def add_result(self, result):
        """
        Add a search result to the treeview.
        This method is called from scanner threads, so we use a queue
        to safely pass the result to the main thread.

        Args:
            result (SearchResult): Search result to add
        """
        # Skip archive markers
        if result.keyword == config.ARCHIVE_MARKER_KEYWORD:
            return

        # Log the result for debugging
        logger.info(f"Match in text {result.file} [Line {result.location}]: {result.keyword}")

        # Create a unique identifier for this result to avoid duplicates
        result_id = f"{result.file}:{result.location}:{result.keyword}"

        # Store the result with its unique ID in a tuple
        self.result_queue.put((result_id, result))

        # Schedule UI update if not already scheduled
        self._schedule_ui_update()

    def _schedule_ui_update(self):
        """
        Schedule a UI update to process results in the queue.
        This method ensures we don't update the UI too frequently.
        """
        # If an update is already scheduled, do nothing
        if self.update_timer is not None:
            return

        # Schedule an update after a short delay
        self.update_timer = self.root.after(
            config.UI_UPDATE_THROTTLE_MS,
            self._process_result_queue
        )

    @handle_ui_operation(error_message="Error processing search results")
    def _process_result_queue(self):
        """
        Process results from the queue and update the UI.
        This method runs on the main thread.
        """
        # Reset the timer
        self.update_timer = None

        # Set to track processed result IDs to avoid duplicates
        if not hasattr(self, 'processed_result_ids'):
            self.processed_result_ids = set()

        # Process up to a batch of results
        results_to_process = []
        batch_size = config.UI_UPDATE_BATCH_SIZE

        # Get results from the queue (up to batch size)
        while len(results_to_process) < batch_size and not self.result_queue.empty():
            try:
                result_data = self.result_queue.get_nowait()

                # Handle both old-style (result only) and new-style (id, result) queue items
                if isinstance(result_data, tuple) and len(result_data) == 2:
                    result_id, result = result_data

                    # Skip if we've already processed this result
                    if result_id in self.processed_result_ids:
                        continue

                    # Add to processed set
                    self.processed_result_ids.add(result_id)
                else:
                    # Old-style result (no ID)
                    result = result_data

                results_to_process.append(result)
                self.results.append(result)  # Add to main results list
            except queue.Empty:
                break

        # If no results, nothing to do
        if not results_to_process:
            return

        # Add results to the treeview
        for i, result in enumerate(results_to_process):
            values = result.get_tree_values()

            # Use a unique ID for each treeview item (without using result_index)
            item_id = self.tree.insert("", tk.END, values=values)

            # Make the last item visible
            if i == len(results_to_process) - 1:
                self.tree.see(item_id)

        # Update UI state
        self.export_button["state"] = tk.NORMAL
        self.clear_button["state"] = tk.NORMAL

        # Update the UI
        self.root.update_idletasks()

        # If there are more results in the queue, schedule another update
        if not self.result_queue.empty():
            self._schedule_ui_update()
