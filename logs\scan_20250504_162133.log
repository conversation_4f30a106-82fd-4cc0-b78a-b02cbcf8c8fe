2025-05-04 16:21:33,873 - INFO - PyPDF2 configured. PDF parsing support enabled.
2025-05-04 16:21:33,906 - INFO - win32com configured. DOC parsing support enabled.
2025-05-04 16:21:33,911 - DEBUG - antiword not available as fallback for DOC files.
2025-05-04 16:21:34,213 - INFO - Application started
2025-05-04 16:21:34,215 - INFO - Custom styling applied
2025-05-04 16:21:34,224 - INFO - rarfile configured. RAR parsing support enabled.
2025-05-04 16:21:34,434 - INFO - py7zr configured. 7z archive parsing support enabled.
2025-05-04 16:21:34,434 - DEBUG - Registered parser: Archive Parser
2025-05-04 16:21:34,434 - DEBUG - Registered parser: CSV Parser
2025-05-04 16:21:34,434 - DEBUG - Registered parser: DOC Parser
2025-05-04 16:21:34,434 - DEBUG - Registered parser: DOCX Parser
2025-05-04 16:21:34,434 - DEBUG - Registered parser: Excel Parser
2025-05-04 16:21:34,434 - DEBUG - Registered parser: ODS Parser
2025-05-04 16:21:34,434 - DEBUG - Registered parser: PDF Parser
2025-05-04 16:21:34,434 - DEBUG - Registered parser: Text Parser
2025-05-04 16:21:34,434 - INFO - Discovered 8 parser plugins
2025-05-04 16:21:34,463 - INFO - Looking for logo at (method 1): C:\Users\<USER>\Desktop\L457\SearchTools-v1\icon.ico
2025-05-04 16:21:34,463 - INFO - Logo file found at: C:\Users\<USER>\Desktop\L457\SearchTools-v1\icon.ico
2025-05-04 16:21:34,596 - INFO - Image opened, mode: RGBA, size: (159, 31)
2025-05-04 16:21:34,598 - INFO - Resized image to 100x19 to maintain aspect ratio
2025-05-04 16:21:34,610 - INFO - Converted to PhotoImage
2025-05-04 16:21:34,613 - INFO - Bottom logo loaded successfully
2025-05-04 16:21:34,776 - INFO - UI initialized
