"""
Test script for SearchResult refactoring
"""

from searchtools.core.result import SearchResult

def test_search_result():
    """Test the SearchResult class"""
    print("Testing SearchResult class...")
    
    # Create a search result
    result = SearchResult(
        file="test.txt",
        path="/path/to/test.txt",
        keyword="hello",
        context="This is a test file that contains the keyword hello in it.",
        location="Line 5",
        full_context="Full context: This is a test file that contains the keyword hello in it."
    )
    
    # Test to_dict method
    result_dict = result.to_dict()
    print(f"Result as dictionary: {result_dict}")
    
    # Test get_display_context method with short context
    display_context = result.get_display_context(max_length=100)
    print(f"Display context (short): {display_context}")
    
    # Test get_display_context method with very short max_length
    display_context_truncated = result.get_display_context(max_length=20)
    print(f"Display context (truncated): {display_context_truncated}")
    
    # Test get_display_context method with no location
    result_no_location = SearchResult(
        file="test.txt",
        path="/path/to/test.txt",
        keyword="hello",
        context="This is a test file that contains the keyword hello in it.",
        location="",
        full_context="Full context: This is a test file that contains the keyword hello in it."
    )
    display_context_no_location = result_no_location.get_display_context()
    print(f"Display context (no location): {display_context_no_location}")
    
    # Test get_tree_values method
    tree_values = result.get_tree_values()
    print(f"Tree values: {tree_values}")
    
    print("\nAll tests completed.")

if __name__ == "__main__":
    test_search_result()
