"""
Logging guidelines for SearchTools.

This module defines the standard logging levels and when to use them.
It also provides helper functions for consistent logging.

Logging Levels:
- DEBUG: Detailed information, typically useful only for diagnosing problems
- INFO: Confirmation that things are working as expected
- WARNING: An indication that something unexpected happened, or may happen in the near future
- ERROR: Due to a more serious problem, the software has not been able to perform a function
- CRITICAL: A serious error, indicating that the program itself may be unable to continue running

Guidelines:
- Use DEBUG for detailed diagnostic information
- Use INFO for general operational information
- Use WARNING for unexpected situations that don't prevent operation
- Use ERROR for failures that prevent a specific operation but not the whole application
- Use CRITICAL for failures that might prevent the application from continuing
"""

from typing import Any, Optional
from searchtools.utils.logging import logger

# Define standard log messages for common scenarios
class LogMessages:
    """Standard log messages for common scenarios."""

    # Parser-related messages
    PARSER_NOT_AVAILABLE = "{parser_name} parsing not available for {file_name}"
    PARSER_FAILED = "{parser_name} parsing failed for {file_name}: {error}"
    PARSER_FALLBACK = "Falling back to {fallback_name} for {file_name}"
    PARSER_SUCCESS = "Successfully parsed {file_name} with {parser_name}"

    # File operation messages
    FILE_NOT_FOUND = "File not found: {file_path}"
    FILE_ACCESS_ERROR = "Cannot access file: {file_path}"
    FILE_READ_ERROR = "Error reading file: {file_path}"
    FILE_WRITE_ERROR = "Error writing file: {file_path}"

    # Archive operation messages
    ARCHIVE_EXTRACTION_STARTED = "Extracting {archive_type} archive: {file_name}"
    ARCHIVE_EXTRACTION_COMPLETED = "Extracted {archive_type} to: {temp_dir}"
    ARCHIVE_EXTRACTION_FAILED = "Failed to extract {archive_type} archive {file_name}: {error}"

    # Dependency messages
    DEPENDENCY_MISSING = "{dependency_name} not installed. {feature_name} support disabled."
    DEPENDENCY_ERROR = "Error during {dependency_name} setup: {error}"
    DEPENDENCY_CONFIGURED = "{dependency_name} configured. {feature_name} support enabled."

    # Search operation messages
    SEARCH_MATCH_FOUND = "Match in {file_type} {file_name} [{location}]: '{keyword}'"
    SEARCH_COMPLETED = "Completed scanning {item_count} {item_type} in {file_name}"
    SEARCH_STARTED = "Starting scan of: {folder_path}"
    SEARCH_FINISHED = "Scan {status}. Found {match_count} matches."

def log_parser_not_available(parser_name: str, file_name: str) -> None:
    """
    Log that a parser is not available for a file.

    Args:
        parser_name (str): Name of the parser
        file_name (str): Name of the file
    """
    logger.warning(LogMessages.PARSER_NOT_AVAILABLE.format(
        parser_name=parser_name,
        file_name=file_name
    ))

def log_parser_error(parser_name: str, file_name: str, error: Exception) -> None:
    """
    Log a parser error.

    Args:
        parser_name (str): Name of the parser
        file_name (str): Name of the file
        error (Exception): The error that occurred
    """
    logger.error(LogMessages.PARSER_FAILED.format(
        parser_name=parser_name,
        file_name=file_name,
        error=str(error)
    ))

def log_parser_fallback(fallback_name: str, file_name: str) -> None:
    """
    Log a parser fallback.

    Args:
        fallback_name (str): Name of the fallback parser
        file_name (str): Name of the file
    """
    logger.info(LogMessages.PARSER_FALLBACK.format(
        fallback_name=fallback_name,
        file_name=file_name
    ))

def log_file_error(error_type: str, file_path: str, error: Optional[Exception] = None) -> None:
    """
    Log a file operation error.

    Args:
        error_type (str): Type of error (not_found, access_error, read_error, write_error)
        file_path (str): Path to the file
        error (Exception, optional): The error that occurred
    """
    if error_type == "not_found":
        logger.error(LogMessages.FILE_NOT_FOUND.format(file_path=file_path))
    elif error_type == "access_error":
        logger.error(LogMessages.FILE_ACCESS_ERROR.format(file_path=file_path))
    elif error_type == "read_error":
        logger.error(LogMessages.FILE_READ_ERROR.format(file_path=file_path))
    elif error_type == "write_error":
        logger.error(LogMessages.FILE_WRITE_ERROR.format(file_path=file_path))
    else:
        logger.error(f"File error ({error_type}): {file_path}" + (f" - {error}" if error else ""))

def log_archive_operation(operation_type: str, archive_type: str, file_name: str,
                         temp_dir: Optional[str] = None, error: Optional[Exception] = None) -> None:
    """
    Log an archive operation.

    Args:
        operation_type (str): Type of operation (started, completed, failed)
        archive_type (str): Type of archive (ZIP, RAR, 7z)
        file_name (str): Name of the archive file
        temp_dir (str, optional): Path to the temporary directory
        error (Exception, optional): The error that occurred
    """
    if operation_type == "started":
        logger.info(LogMessages.ARCHIVE_EXTRACTION_STARTED.format(
            archive_type=archive_type,
            file_name=file_name
        ))
    elif operation_type == "completed":
        logger.info(LogMessages.ARCHIVE_EXTRACTION_COMPLETED.format(
            archive_type=archive_type,
            temp_dir=temp_dir
        ))
    elif operation_type == "failed":
        logger.error(LogMessages.ARCHIVE_EXTRACTION_FAILED.format(
            archive_type=archive_type,
            file_name=file_name,
            error=str(error)
        ))
    else:
        logger.info(f"{archive_type} archive {operation_type}: {file_name}")

def log_dependency_status(status_type: str, dependency_name: str, feature_name: str,
                         error: Optional[Exception] = None) -> None:
    """
    Log a dependency status.

    Args:
        status_type (str): Type of status (missing, error, configured)
        dependency_name (str): Name of the dependency
        feature_name (str): Name of the feature that depends on it
        error (Exception, optional): The error that occurred
    """
    if status_type == "missing":
        logger.warning(LogMessages.DEPENDENCY_MISSING.format(
            dependency_name=dependency_name,
            feature_name=feature_name
        ))
    elif status_type == "error":
        logger.warning(LogMessages.DEPENDENCY_ERROR.format(
            dependency_name=dependency_name,
            error=str(error)
        ))
    elif status_type == "configured":
        logger.info(LogMessages.DEPENDENCY_CONFIGURED.format(
            dependency_name=dependency_name,
            feature_name=feature_name
        ))
    else:
        logger.info(f"{dependency_name} {status_type}: {feature_name}")

def log_search_match(file_type: str, file_name: str, location: str, keyword: str) -> None:
    """
    Log a search match.

    Args:
        file_type (str): Type of file (PDF, Excel, etc.)
        file_name (str): Name of the file
        location (str): Location within the file
        keyword (str): Found keyword
    """
    logger.info(LogMessages.SEARCH_MATCH_FOUND.format(
        file_type=file_type,
        file_name=file_name,
        location=location,
        keyword=keyword
    ))

def log_search_operation(operation_type: str, **kwargs: Any) -> None:
    """
    Log a search operation.

    Args:
        operation_type (str): Type of operation (started, completed, finished)
        **kwargs: Additional parameters for the log message
    """
    if operation_type == "started":
        logger.info(LogMessages.SEARCH_STARTED.format(folder_path=kwargs.get('folder_path', 'unknown')))
    elif operation_type == "completed":
        logger.info(LogMessages.SEARCH_COMPLETED.format(
            item_count=kwargs.get('item_count', 0),
            item_type=kwargs.get('item_type', 'items'),
            file_name=kwargs.get('file_name', 'unknown')
        ))
    elif operation_type == "finished":
        logger.info(LogMessages.SEARCH_FINISHED.format(
            status=kwargs.get('status', 'complete'),
            match_count=kwargs.get('match_count', 0)
        ))
    else:
        logger.info(f"Search {operation_type}: {kwargs}")
