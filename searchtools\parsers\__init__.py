"""
File parsers for different file types
"""

# Import all parsers to make them available for discovery
from searchtools.parsers.base_parser import BaseParser
from searchtools.parsers.text_parser import TextParser
from searchtools.parsers.excel_parser import ExcelParser
from searchtools.parsers.csv_parser import CSVParser
from searchtools.parsers.docx_parser import DocxParser
from searchtools.parsers.pdf_parser import PDFParser
from searchtools.parsers.archive_parser import ArchiveParser
from searchtools.parsers.doc_parser import DocParser
from searchtools.parsers.ods_parser import OdsParser

# Import the plugin manager
from searchtools.parsers.plugin_manager import plugin_manager

__all__ = [
    'BaseParser',
    'TextParser',
    'ExcelParser',
    'CSVParser',
    'DocxParser',
    'PDFParser',
    'ArchiveParser',
    'DocParser',
    'OdsParser',
    'plugin_manager',
]
