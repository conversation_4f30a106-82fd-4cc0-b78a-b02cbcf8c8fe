"""
Main entry point for the SearchTools application
"""

import os
import tkinter as tk
from searchtools.ui.main_window import MainWindow
from searchtools.utils.logging import logger

# We'll use the standard tkinter without external theme packages

def main():
    """
    Main entry point for the application.
    """
    try:
        # Set up the root window
        root = tk.Tk()

        # Set icon if available
        try:
            icon_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "icon.ico")
            if os.path.exists(icon_path):
                root.iconbitmap(icon_path)
        except (tk.TclError, FileNotFoundError) as e:
            logger.debug(f"Could not set application icon: {e}")  # Log the error but continue

        # Log application start
        logger.info("Application started")

        # Create the main window
        MainWindow(root)  # No need to store the reference as it's attached to root

        # Start the main loop
        root.mainloop()

    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        import traceback
        logger.debug(traceback.format_exc())
    finally:
        # Log application exit
        logger.info("Application exited")

if __name__ == "__main__":
    main()
