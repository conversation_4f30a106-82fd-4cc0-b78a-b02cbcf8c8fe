"""
Unit tests for CLI
"""

import os
import unittest
import tempfile
import csv
import json
from typing import List, Dict, Any

from searchtools.cli.cli import CLI

class TestCLI(unittest.TestCase):
    """
    Test case for CLI.
    """
    
    def setUp(self):
        """
        Set up the test case.
        """
        # Create a temporary directory for test files
        self.temp_dir = tempfile.mkdtemp()
        
        # Create a test text file
        self.text_file_path = os.path.join(self.temp_dir, "test.txt")
        with open(self.text_file_path, "w") as f:
            f.write("This is a test file.\n")
            f.write("It contains some test keywords.\n")
            f.write("The keywords are: test, keyword, file.\n")
        
        # Create a CLI instance
        self.cli = CLI()
    
    def tearDown(self):
        """
        Clean up after the test case.
        """
        # Remove the temporary directory
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_cli_run(self):
        """
        Test running the CLI.
        """
        # Create a temporary output file
        fd, output_file = tempfile.mkstemp()
        os.close(fd)
        
        try:
            # Run the CLI
            args = [
                self.temp_dir,
                "test,keyword",
                "--output", output_file,
                "--format", "csv"
            ]
            exit_code = self.cli.run(args)
            
            # Check the exit code
            self.assertEqual(exit_code, 0, "CLI returned non-zero exit code")
            
            # Check that the output file was created
            self.assertTrue(os.path.exists(output_file), "Output file was not created")
            
            # Check the output file content
            with open(output_file, "r") as f:
                reader = csv.reader(f)
                rows = list(reader)
                
                # Check the header
                self.assertEqual(rows[0], ["File", "Path", "Keyword", "Context", "Location"], "CSV header is incorrect")
                
                # Check that there are results
                self.assertGreater(len(rows), 1, "No results in the output file")
                
                # Check that the results contain the expected keywords
                keywords = [row[2] for row in rows[1:]]
                self.assertTrue(all(k in ["test", "keyword"] for k in keywords), "Results contain unexpected keywords")
        finally:
            # Remove the output file
            if os.path.exists(output_file):
                os.unlink(output_file)
    
    def test_cli_json_output(self):
        """
        Test JSON output from the CLI.
        """
        # Create a temporary output file
        fd, output_file = tempfile.mkstemp()
        os.close(fd)
        
        try:
            # Run the CLI
            args = [
                self.temp_dir,
                "test,keyword",
                "--output", output_file,
                "--format", "json"
            ]
            exit_code = self.cli.run(args)
            
            # Check the exit code
            self.assertEqual(exit_code, 0, "CLI returned non-zero exit code")
            
            # Check that the output file was created
            self.assertTrue(os.path.exists(output_file), "Output file was not created")
            
            # Check the output file content
            with open(output_file, "r") as f:
                data = json.load(f)
                
                # Check that there are results
                self.assertGreater(len(data), 0, "No results in the output file")
                
                # Check that the results contain the expected fields
                for result in data:
                    self.assertIn("file", result, "Result missing 'file' field")
                    self.assertIn("path", result, "Result missing 'path' field")
                    self.assertIn("keyword", result, "Result missing 'keyword' field")
                    self.assertIn("context", result, "Result missing 'context' field")
                    self.assertIn("location", result, "Result missing 'location' field")
                
                # Check that the results contain the expected keywords
                keywords = [result["keyword"] for result in data]
                self.assertTrue(all(k in ["test", "keyword"] for k in keywords), "Results contain unexpected keywords")
        finally:
            # Remove the output file
            if os.path.exists(output_file):
                os.unlink(output_file)
    
    def test_cli_invalid_folder(self):
        """
        Test running the CLI with an invalid folder.
        """
        # Run the CLI with a non-existent folder
        args = [
            "/non/existent/folder",
            "test,keyword"
        ]
        exit_code = self.cli.run(args)
        
        # Check the exit code
        self.assertEqual(exit_code, 1, "CLI did not return error code for invalid folder")
    
    def test_cli_no_keywords(self):
        """
        Test running the CLI with no keywords.
        """
        # Run the CLI with empty keywords
        args = [
            self.temp_dir,
            ""
        ]
        exit_code = self.cli.run(args)
        
        # Check the exit code
        self.assertEqual(exit_code, 1, "CLI did not return error code for empty keywords")

if __name__ == "__main__":
    unittest.main()
