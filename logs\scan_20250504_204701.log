2025-05-04 20:47:03,356 - INFO - PyPDF2 configured. PDF parsing support enabled.
2025-05-04 20:47:03,416 - INFO - win32com configured. DOC parsing support enabled.
2025-05-04 20:47:03,428 - DEBUG - antiword not available as fallback for DOC files.
2025-05-04 20:47:03,752 - INFO - Application started
2025-05-04 20:47:03,754 - INFO - Custom styling applied
2025-05-04 20:47:03,763 - INFO - rarfile configured. RAR parsing support enabled.
2025-05-04 20:47:04,220 - INFO - py7zr configured. 7z archive parsing support enabled.
2025-05-04 20:47:04,222 - DEBUG - Registered parser: Archive Parser
2025-05-04 20:47:04,222 - DEBUG - Registered parser: CSV Parser
2025-05-04 20:47:04,222 - DEBUG - Registered parser: DOC Parser
2025-05-04 20:47:04,222 - DEBUG - Registered parser: DOCX Parser
2025-05-04 20:47:04,226 - DEBUG - Registered parser: Excel Parser
2025-05-04 20:47:04,226 - DEBUG - Registered parser: ODS Parser
2025-05-04 20:47:04,226 - DEBUG - Registered parser: PDF Parser
2025-05-04 20:47:04,226 - DEBUG - Registered parser: Text Parser
2025-05-04 20:47:04,226 - INFO - Discovered 8 parser plugins
2025-05-04 20:47:04,279 - INFO - Looking for logo at (method 1): c:\Users\<USER>\Desktop\L457\SearchTools-v1\icon.ico
2025-05-04 20:47:04,281 - INFO - Logo file found at: c:\Users\<USER>\Desktop\L457\SearchTools-v1\icon.ico
2025-05-04 20:47:04,467 - INFO - Image opened, mode: RGBA, size: (159, 31)
2025-05-04 20:47:04,472 - INFO - Resized image to 100x19 to maintain aspect ratio
2025-05-04 20:47:04,486 - INFO - Converted to PhotoImage
2025-05-04 20:47:04,497 - INFO - Bottom logo loaded successfully
2025-05-04 20:47:04,777 - INFO - UI initialized
2025-05-04 21:01:46,135 - INFO - Application interrupted by user
2025-05-04 21:01:46,140 - INFO - Application exited
